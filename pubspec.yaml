name: rider
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+4

environment:
  sdk: 3.6.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  # cupertino_icons: ^1.0.8
  onesignal_flutter: 5.2.9
  cloud_firestore: 5.4.0
  flutter_stripe: 11.0.0
  state_beacon: 1.0.0
  pay: 2.0.0
  fluttertoast: 8.2.12
  cached_network_image: 3.4.0
  shared_preferences: 2.3.2
  pinput: 5.0.0
  sliding_up_panel: 2.0.0+1
  dotted_line: 3.2.2
  flutter_rating_bar: 4.0.1
  flutter_staggered_animations: 1.1.1
  intl_phone_field: 3.2.0
  intl: 0.19.0
  flutter_native_contact_picker: 0.0.8
  dotted_border: 2.1.0
  timeline_tile: 2.0.0
  flutter_vector_icons: 2.0.0
  smooth_page_indicator: 1.2.0+3
  webview_flutter: 4.8.0
  otp_text_field: 1.1.3
  location: 7.0.0
  geocoding: 3.0.0
  # google_maps_flutter: 2.9.0
  animated_marker: 0.1.4
  flutter_polyline_points: 2.1.0
  # google_maps_place_picker_mb: 3.1.2
  image_picker: 1.1.2
  file_picker: 8.1.2
  # package_info_plus: 8.0.0
  http: 1.2.2
  connectivity_plus: 6.1.1
  url_launcher: 6.3.0
  firebase_core: 3.6.0
  mqtt_client: 10.5.1
  chucker_flutter: 1.8.1
  # sign_in_with_apple: 5.0.0
  flex_color_scheme: 8.0.2
  flutter_spinkit: 5.2.1
  permission_handler: 11.3.1
  # ultra_map_place_picker: 0.0.5
  timer_count_down: 2.2.2
  countdown_progress_indicator: 0.1.3
  # firebase_pagination: 4.0.1
  paginate_firestore_plus: 1.0.2+3
  share_plus: 10.0.2
  # agora_rtc_engine: 6.3.2c
  
  google_sign_in: 6.2.1
  sign_in_with_apple: 6.1.3
  flutter_facebook_auth: 7.1.1
  zego_uikit_prebuilt_call: 4.16.22
  zego_uikit_signaling_plugin: 2.8.10
  zego_express_engine: 3.19.0
  zego_zim: 2.19.0
  firebase_messaging: 15.1.3
  bottom_picker: 2.9.0
  motion_toast: 2.11.0
  firebase_auth: ^5.3.1
  aad_oauth: 1.0.1
  tutorial_coach_mark: 1.2.12
  uuid: 4.5.1

  flutter_localizations:
    sdk: flutter
  flutter_timezone: ^3.0.1
  flutter_bloc: 9.1.0
  mapbox_maps_flutter: 2.5.0
  


dependency_overrides:
  web: 1.1.0
  js: 0.7.1


  flutter_callkit_incoming_yoer:
    path: ./other_packages/flutter_callkit_incoming_yoer

dev_dependencies:
  
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: 4.0.0

  flutter_launcher_icons: 0.14.1


flutter_launcher_icons:
  image_path: "assets/app_logo.png"
  image_path_ios: "assets/app_logo.png"
  android: true
  adaptive_icon_foreground: "assets/app_logo.png"
  adaptive_icon_background: "#ffffff"
  ios: true
  remove_alpha_ios: true
  min_sdk_android: 21
  


# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/logo-white.png
    - assets/logo-black.png
    - assets/images/
    - assets/lottie/

    - assets/placeholders/
    - assets/ms-login/ms.json
    - assets/walkthrough/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

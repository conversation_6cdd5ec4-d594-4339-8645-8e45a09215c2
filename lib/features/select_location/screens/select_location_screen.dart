import 'package:geocoding/geocoding.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:rider/app_exports.dart';
import 'package:location/location.dart' as nlp;
import 'package:rider/features/ride-settings/screens/personal_or_business_ride_screen.dart';
import 'package:rider/features/select_location/models/mapbox_location_model.dart';
import 'package:uuid/uuid.dart';

class SelectLocationScreen extends StatefulWidget {
  final DateTime? scheduledTime;
  const SelectLocationScreen({
    super.key,
    required this.scheduledTime,
  });

  @override
  State<SelectLocationScreen> createState() => SelectLocationScreenState();
}

class SelectLocationScreenState extends State<SelectLocationScreen> {
  bool _showPickUpAddress = true;
  bool _showDestinationAddress = true;
  final _pickupController = TextEditingController();
  final int _personCount = 1;

  List<MapBoxSuggestion> _addressList = [];

  late mp.Position _currentLocation;

  String _pickupAdress = "";
  String _destinationAdress = "";

  String _pickupLattitude = "";
  String _pickupLongitude = "";

  final _pickpFocus = FocusNode();

  final _destinationController = TextEditingController();

  final _destinationFocus = FocusNode();
  String _destinationLattitude = "";
  String _destinationLongitude = "";
  Timer? _debounceTimer;
  bool _isSearchingPickUp = false;
  bool _isSearchingDestination = false;

  final String _sessionToken = const Uuid().v4();

  void _selectAllText(TextEditingController controller) {
    controller.selection = TextSelection(
      baseOffset: 0,
      extentOffset: controller.text.length,
    );
  }

  _getSearchResults({required String searchText}) {
    searchWithMapBox(
      search: searchText,
      sessionToken: _sessionToken,
      proximity: _currentLocation,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      if (_isSearchingPickUp || _isSearchingDestination) {
        _isSearchingDestination = false;
        _isSearchingPickUp = false;

        _addressList = [];
        for (var element in value.suggestions) {
          if (element.fullAddress.isNotEmpty) {
            _addressList.add(element);
          }
        }
        if (_addressList.isEmpty) {
          _addressList.add(MapBoxSuggestion(
              name: "No results found, Please select from map.",
              fullAddress: "",
              mapBoxId: "-1"));
        }

        setState(() {
          // update
        });
      }
    });
  }

  Future<void> _onSearching(
      BuildContext context, String searchText, bool isPickUp) async {
    setState(() {
      if (isPickUp) {
        _isSearchingPickUp = true;
      } else {
        _isSearchingDestination = true;
      }
    });
    // Cancel the previous debounce timer if it exists to prevent extra calls
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    // Start a new debounce timer
    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _getSearchResults(searchText: searchText);
      //Make API call or do something
    });
  }

  _onSelectPickup({required String mapBoxId}) async {
    showAppActivity();
    await getMapBoxLocation(mapBoxId: mapBoxId, sessionToken: _sessionToken)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }

      _pickupLattitude = value.latitude.toString();
      _pickupLongitude = value.longitude.toString();
      _pickupAdress = "${value.name}, ${value.fullAddress}";

      _addressList = [];
      _destinationFocus.requestFocus();
      setState(() {
        _pickupController.text = _pickupAdress;
        // _pickupController.clear();
      });
    });
    hideKeyboard();
    hideAppActivity();
  }

  _onSelectDestination({required String mapBoxId}) async {
    showAppActivity();
    await getMapBoxLocation(mapBoxId: mapBoxId, sessionToken: _sessionToken)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      _destinationLattitude = value.latitude.toString();
      _destinationLongitude = value.longitude.toString();

      _destinationAdress = "${value.name}, ${value.fullAddress}";
      _addressList = [];
      setState(() {
        _destinationController.text = _destinationAdress;
        // _destinationController.clear();
      });
    });

    hideKeyboard();
    hideAppActivity();
  }

  _dispose() {
    _debounceTimer?.cancel();
  }

  @override
  void initState() {
    _pickpFocus.addListener(() {
      if (_pickpFocus.hasFocus) {
        Scrollable.ensureVisible(
          _pickpFocus.context!,
          alignment: 0,
          duration: const Duration(milliseconds: 300),
        );
        _showPickUpAddress = false;
        _selectAllText(_pickupController);
      } else {
        _showPickUpAddress = true;
      }
      setState(() {
        //
      });
    });
    _destinationFocus.addListener(() {
      if (_destinationFocus.hasFocus) {
        Scrollable.ensureVisible(
          _destinationFocus.context!,
          alignment: 0,
          duration: const Duration(milliseconds: 300),
        );
        _showDestinationAddress = false;
        _selectAllText(_destinationController);
      } else {
        Scrollable.ensureVisible(
          _pickpFocus.context!,
          alignment: 0.1,
          duration: const Duration(milliseconds: 300),
        );
        _showDestinationAddress = true;
      }
      setState(() {
        //
      });
    });
    _fillPickUpLocation();
    _loadRecentLocations();
    super.initState();
  }

  Future<void> _getCurrentLocation() async {
    if (Globals.currentLocation == null) {
      nlp.Location location = nlp.Location();
      await location.getLocation().then((locationData) async {
        Globals.currentLocation =
            mp.Position(locationData.longitude!, locationData.latitude!);
        mp.Position data =
            mp.Position(locationData.longitude!, locationData.latitude!);

        _currentLocation = data;
        _pickupLattitude = data.lat.toString();
        _pickupLongitude = data.lng.toString();
      });
    } else {
      _currentLocation = Globals.currentLocation!;
      _pickupLattitude = Globals.currentLocation!.lat.toString();
      _pickupLongitude = Globals.currentLocation!.lng.toString();
    }
  }

  Future<void> _fillPickUpLocation() async {
    showAppActivity();
    await _getCurrentLocation();

    await placemarkFromCoordinates(
            _currentLocation.lat.toDouble(), _currentLocation.lng.toDouble())
        .then((details) {
      if (details.isEmpty) {
        throw "";
      }
      Placemark place = details[0];
      _pickupAdress =
          "${place.name ?? place.subThoroughfare}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}";

      _pickupController.text = _pickupAdress;
    });

    hideAppActivity();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
    _dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        bottomNavigationBar: Visibility(
          visible: _pickupAdress.isNotEmpty && _destinationAdress.isNotEmpty,
          child: BottomButton(
            text: "Next",
            onPressed: () {
              _goNext();
            },
          ),
        ),
        appBar: const RoooAppbar(title: "Select Location"),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: EdgeInsets.fromLTRB(
                    16.0, 16, 16, (_addressList.isEmpty) ? 500 : 40),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        color: Globals.isDarkModeOn
                            ? Colors.grey[900]
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Pickup Location",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Globals.isDarkModeOn
                                  ? Colors.white
                                  : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            controller: _pickupController,
                            focusNode: _pickpFocus,
                            onChanged: (value) {
                              if (value.length >= 3) {
                                _onSearching(context, value, true);
                              } else {
                                setState(() {
                                  _isSearchingPickUp = false;
                                  _addressList = [];
                                });
                              }
                            },
                            decoration: InputDecoration(
                              hintText: "Enter pickup location",
                              prefixIcon: Icon(
                                Icons.location_on,
                                color: Theme.of(context).primaryColor,
                              ),
                              suffixIcon: _isSearchingPickUp
                                  ? const SizedBox(
                                      width: 20,
                                      child: RooLoader(size: 20),
                                    )
                                  : const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Globals.isDarkModeOn
                                  ? Colors.grey[800]
                                  : Colors.white,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton.icon(
                                onPressed: () async {
                                  hideKeyboard();
                                  MapBoxLocationModel? selectedPlace =
                                      await launchScreen(
                                          const MapPickupScreen());
                                  if (selectedPlace != null) {
                                    _pickupLattitude = selectedPlace
                                        .point!.latitude
                                        .toString();
                                    _pickupLongitude = selectedPlace
                                        .point!.longitude
                                        .toString();
                                    setState(() {
                                      _pickupAdress =
                                          selectedPlace.address.toString();
                                      _pickupController.text = _pickupAdress;
                                      _addressList.clear();
                                    });
                                  }
                                  hideKeyboard();
                                },
                                icon: Icon(
                                  Icons.map,
                                  color: Theme.of(context).primaryColor,
                                ),
                                label: Text(
                                  "From map",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  backgroundColor: Globals.isDarkModeOn
                                      ? Colors.grey[800]
                                      : Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                              TextButton.icon(
                                onPressed: () async {
                                  hideKeyboard();
                                  SavedPlace? savedPlace = await launchScreen(
                                    const SavedPlacesScreen(),
                                    context: context,
                                  );
                                  if (savedPlace != null) {
                                    _pickupLattitude = savedPlace.latitude;
                                    _pickupLongitude = savedPlace.longitude;
                                    setState(() {
                                      _pickupAdress = savedPlace.title;
                                      _pickupController.text = _pickupAdress;
                                      _addressList.clear();
                                    });
                                  }
                                  hideKeyboard();
                                },
                                icon: Icon(
                                  Icons.flag,
                                  color: Theme.of(context).primaryColor,
                                ),
                                label: Text(
                                  "Favorites",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  backgroundColor: Globals.isDarkModeOn
                                      ? Colors.grey[800]
                                      : Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    if (_addressList.isNotEmpty && _pickpFocus.hasFocus)
                      Padding(
                        padding: const EdgeInsets.only(top: 16),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Globals.isDarkModeOn
                                ? Colors.grey[900]
                                : Colors.white,
                            borderRadius: BorderRadius.circular(12),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 8,
                                offset: const Offset(0, 2),
                              ),
                            ],
                          ),
                          child: ListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _addressList.length,
                            itemBuilder: (context, index) {
                              MapBoxSuggestion data = _addressList[index];
                              return ListTile(
                                onTap: () async {
                                  if (data.mapBoxId == "-1") {
                                    hideKeyboard();
                                    _pickupController.text = "";
                                    setState(() {
                                      _addressList = [];
                                    });
                                    return;
                                  }
                                  _onSelectPickup(mapBoxId: data.mapBoxId);
                                },
                                leading: Icon(
                                  Icons.location_on_outlined,
                                  color: Theme.of(context).primaryColor,
                                ),
                                title: Text(
                                  data.name,
                                  style: TextStyle(
                                    color: Globals.isDarkModeOn
                                        ? Colors.white
                                        : Colors.black87,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                subtitle: Text(
                                  data.fullAddress,
                                  style: TextStyle(
                                    color: Globals.isDarkModeOn
                                        ? Colors.grey[400]
                                        : Colors.grey[600],
                                    fontSize: 12,
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                    const SizedBox(height: 16),
                    // Destination Location Section
                    Container(
                      decoration: BoxDecoration(
                        color: Globals.isDarkModeOn
                            ? Colors.grey[900]
                            : Colors.grey[100],
                        borderRadius: BorderRadius.circular(12),
                      ),
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Destination",
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: Globals.isDarkModeOn
                                  ? Colors.white
                                  : Colors.black87,
                            ),
                          ),
                          const SizedBox(height: 12),
                          TextField(
                            controller: _destinationController,
                            focusNode: _destinationFocus,
                            onChanged: (value) {
                              if (value.length >= 3) {
                                _onSearching(context, value, false);
                              } else {
                                setState(() {
                                  _isSearchingDestination = false;
                                  _addressList = [];
                                });
                              }
                            },
                            decoration: InputDecoration(
                              hintText: "Enter destination",
                              prefixIcon: Icon(
                                Icons.location_on,
                                color: Theme.of(context).primaryColor,
                              ),
                              suffixIcon: _isSearchingDestination
                                  ? const SizedBox(
                                      width: 20,
                                      child: RooLoader(size: 20),
                                    )
                                  : const Icon(Icons.search),
                              border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(8),
                                borderSide: BorderSide.none,
                              ),
                              filled: true,
                              fillColor: Globals.isDarkModeOn
                                  ? Colors.grey[800]
                                  : Colors.white,
                              contentPadding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 12),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton.icon(
                                onPressed: () async {
                                  hideKeyboard();
                                  MapBoxLocationModel? selectedPlace =
                                      await launchScreen(
                                          const MapPickupScreen());
                                  if (selectedPlace != null) {
                                    _destinationLattitude = selectedPlace
                                        .point!.latitude
                                        .toString();
                                    _destinationLongitude = selectedPlace
                                        .point!.longitude
                                        .toString();
                                    setState(() {
                                      _destinationAdress =
                                          selectedPlace.address.toString();
                                      _destinationController.text =
                                          _destinationAdress;
                                      _addressList.clear();
                                    });
                                  }
                                  hideKeyboard();
                                },
                                icon: Icon(
                                  Icons.map,
                                  color: Theme.of(context).primaryColor,
                                ),
                                label: Text(
                                  "From map",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  backgroundColor: Globals.isDarkModeOn
                                      ? Colors.grey[800]
                                      : Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                              TextButton.icon(
                                onPressed: () async {
                                  hideKeyboard();
                                  SavedPlace? savedPlace = await launchScreen(
                                    const SavedPlacesScreen(),
                                    context: context,
                                  );
                                  if (savedPlace != null) {
                                    _destinationLattitude = savedPlace.latitude;
                                    _destinationLongitude =
                                        savedPlace.longitude;
                                    setState(() {
                                      _destinationAdress = savedPlace.title;
                                      _destinationController.text =
                                          _destinationAdress;
                                      _addressList.clear();
                                    });
                                  }
                                  hideKeyboard();
                                },
                                icon: Icon(
                                  Icons.flag,
                                  color: Theme.of(context).primaryColor,
                                ),
                                label: Text(
                                  "Favorites",
                                  style: TextStyle(
                                    color: Theme.of(context).primaryColor,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                                style: TextButton.styleFrom(
                                  backgroundColor: Globals.isDarkModeOn
                                      ? Colors.grey[800]
                                      : Colors.white,
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 16, vertical: 12),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 16),

                    // Location Suggestions List
                    if (_addressList.isNotEmpty && _destinationFocus.hasFocus)
                      Container(
                        decoration: BoxDecoration(
                          color: Globals.isDarkModeOn
                              ? Colors.grey[900]
                              : Colors.white,
                          borderRadius: BorderRadius.circular(12),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: ListView.builder(
                          shrinkWrap: true,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: _addressList.length,
                          itemBuilder: (context, index) {
                            MapBoxSuggestion data = _addressList[index];
                            return ListTile(
                              onTap: () async {
                                if (data.mapBoxId == "-1") {
                                  hideKeyboard();
                                  _destinationController.text = "";

                                  setState(() {
                                    _addressList = [];
                                  });
                                  return;
                                }
                                _onSelectDestination(mapBoxId: data.mapBoxId);
                              },
                              leading: Icon(
                                Icons.location_on_outlined,
                                color: Theme.of(context).primaryColor,
                              ),
                              title: Text(
                                data.name,
                                style: TextStyle(
                                  color: Globals.isDarkModeOn
                                      ? Colors.white
                                      : Colors.black87,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              subtitle: Text(
                                data.fullAddress,
                                style: TextStyle(
                                  color: Globals.isDarkModeOn
                                      ? Colors.grey[400]
                                      : Colors.grey[600],
                                  fontSize: 12,
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    const SizedBox(height: 16),
                    _lastRidesView(),
                  ],
                ),
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Future<void> _goNext() async {
    launchScreen(
      PersonalOrBusinessRideScreen(
        sourceAddress: _pickupAdress,
        destinationAddress: _destinationAdress,
        pickupLocation: mp.Position(
            double.parse(_pickupLongitude), double.parse(_pickupLattitude)),
        destinlationLocation: mp.Position(
          double.parse(_destinationLongitude),
          double.parse(
            _destinationLattitude,
          ),
        ),
        scheduledTime: widget.scheduledTime,
        isPoolingRide: false,
        personCount: _personCount,
      ),
    );
  }

  bool _isLoadingLastRides = true;
  bool _errorLoadingLocations = false;
  late LastCompletedRidesResponse _lastRides;

  Future<void> _loadRecentLocations() async {
    var result = await getLastCompletedRideLocations();
    _isLoadingLastRides = false;
    if (result == null || !result.status) {
      setState(() {
        _errorLoadingLocations = true;
      });
    } else {
      // // dummy data
      // result.data!.add(MapBoxLocation(
      //   name: "Home 123 Main St, San Francisco, CA 94105, 123 Main St, San Francisco, CA 94105",
      //   latitude: 37.7749,
      //   longitude: -122.4194,
      //   fullAddress: "123 Main St, San Francisco, CA 94105",
      // ));
      // result.data!.add(MapBoxLocation(
      //   name: "Work",
      //   latitude: 37.7749,
      //   longitude: -122.4194,
      //   fullAddress: "123 Main St, San Francisco, CA 94105",
      // ));
      setState(() {
        _lastRides = result;
      });
    }
  }

  Widget _lastRidesView() {
    Widget child = const SizedBox();
    if (_isLoadingLastRides) {
      child = const Center(child: CircularProgressIndicator());
    } else if (_errorLoadingLocations) {
      child = const Center(child: Text("Error loading destinations"));
    } else if (_lastRides.data!.isEmpty) {
      child = const Center(child: Text("No recent destinations"));
    } else {
      child = ListView.separated(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return ListTile(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(10),
            ),
            tileColor:
                Globals.isDarkModeOn ? Colors.grey[800] : Colors.grey[200],
            title: Row(
              children: [
                const Icon(
                  Icons.history,
                ),
                const SizedBox(width: 10),
                Expanded(
                  child: Text(
                    _lastRides.data![index].name,
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                      color: Globals.isDarkModeOn ? Colors.white : Colors.black,
                    ),
                  ),
                ),
              ],
            ),
            onTap: () {
              _destinationAdress = _lastRides.data![index].name;
              _destinationLattitude =
                  _lastRides.data![index].latitude.toString();
              _destinationLongitude =
                  _lastRides.data![index].longitude.toString();
              setState(() {
                _destinationController.text = _destinationAdress;
              });
            },
          );
        },
        separatorBuilder: (context, index) => Divider(
          color: Globals.isDarkModeOn ? Colors.grey[700] : Colors.grey[300],
        ),
        itemCount: _lastRides.data!.length,
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Recent Destinations",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Globals.isDarkModeOn ? Colors.white : Colors.black87,
          ),
        ),
        const SizedBox(height: 10),
        child,
      ],
    );
  }
}

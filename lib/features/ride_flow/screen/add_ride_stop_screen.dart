import 'package:geocoding/geocoding.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mb;
import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:location/location.dart';
import 'package:location/location.dart' as nlp;
import 'package:rider/features/select_location/models/mapbox_location_model.dart';
import 'package:uuid/uuid.dart';

class AddRideStopScreen extends StatefulWidget {
  final int rideId;
  final List<RideStop>? existingStops;
  
  const AddRideStopScreen({
    super.key, 
    required this.rideId,
    this.existingStops,
  });

  @override
  State<AddRideStopScreen> createState() => _AddRideStopScreenState();
}

class _AddRideStopScreenState extends State<AddRideStopScreen> {
  Timer? _debounceTimer;

  List<MapBoxSuggestion> _addressList = [];
  bool _isSearching = false;
  bool _isAddingStop = false;

  final TextEditingController _searchController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  List<RideStop> _rideStops = [];
  RideStop? _currentStop;
  int _nextStopKey = 0; // For unique keys

  final String _sessionToken = const Uuid().v4();

  late mb.Position _currentLocation;

  @override
  void initState() {
    super.initState();

    // Initialize with existing stops
    if (widget.existingStops != null) {
      _rideStops = List.from(widget.existingStops!);
    }

    _focusNode.addListener(() {
      if (_focusNode.hasFocus) {
        _searchController.selection = TextSelection(
          baseOffset: 0,
          extentOffset: _searchController.text.length,
        );
      }
    });

    _fillPickUpLocation();
  }

  @override
  void dispose() {
    super.dispose();
    hideAppActivity();
  }

  Future<void> _fillPickUpLocation() async {
    try {
      if(Globals.currentLocation != null){
        _currentLocation = Globals.currentLocation!;
        return;
      }
      showAppActivity();
      nlp.Location location = nlp.Location();

      await location.getLocation().then((locationData) async {
        _currentLocation =
            mb.Position(locationData.longitude!, locationData.latitude!);
        setState(() {
          hideAppActivity();
        });
      });
    } catch (e) {
      toast(Constants.errorMSG);
    }
  }

  void _startAddingStop() {
    setState(() {
      _isAddingStop = true;
      _currentStop = RideStop(
        id: -1,
        latitude: 0.0,
        longitude: 0.0,
        title: "",
        rideId: widget.rideId,
        currentLatitude: 0,
        currentLongitude: 0,
        currentAddress: "",
        isThePriceIncreasedAfterThisStop: true,
        debitWallet: null,
        paymentCardId: null,
        isArrived: null,
        payablePrice: null,
      );
      _searchController.clear();
      _addressList.clear();
    });
  }

  void _cancelAddingStop() {
    setState(() {
      _isAddingStop = false;
      _currentStop = null;
      _searchController.clear();
      _addressList.clear();
    });
  }

  void _removeStop(int index) async {
    bool? shouldRemove = await showAppDialog(
      dialogType: AppDialogType.confirmation,
      title: "Are you sure you want to remove this stop?",
      onAccept: () => Navigator.of(context).pop(true),
      onCancel: () => Navigator.of(context).pop(false),
    );

    if (shouldRemove == true) {
      setState(() {
        _rideStops.removeAt(index);
      });
    }
  }

  void _reorderStops(int oldIndex, int newIndex) {
    setState(() {
      if (newIndex > oldIndex) {
        newIndex -= 1;
      }
      final item = _rideStops.removeAt(oldIndex);
      _rideStops.insert(newIndex, item);
    });
  }

  void _initializeCurrentStopIfNeeded() {
    _currentStop ??= RideStop(
        id: -1,
        latitude: 0.0,
        longitude: 0.0,
        title: "",
        rideId: widget.rideId,
        currentLatitude: 0,
        currentLongitude: 0,
        currentAddress: "",
        isThePriceIncreasedAfterThisStop: true,
        debitWallet: null,
        paymentCardId: null,
        isArrived: null,
        payablePrice: null,
      );
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: const RoooAppbar(title: "Manage Ride Stops"),
        body: Stack(
          children: [
            SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(
                  Layout.scaffoldBodyPadding,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header with stop count
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  "Ride Stops (${_rideStops.length}/${Constants.maxStopsInARide})",
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                if (_isAddingStop) ...[
                                  const SizedBox(width: 8),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                                    decoration: BoxDecoration(
                                      color: Theme.of(context).primaryColor,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: const Text(
                                      "Adding",
                                      style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 10,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ],
                            ),
                            if (_rideStops.isEmpty)
                              Text(
                                "Add up to 3 stops to your ride",
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.grey[400] 
                                      : Colors.grey[600],
                                ),
                              ),
                          ],
                        ),
                        if (_rideStops.length < Constants.maxStopsInARide && !_isAddingStop)
                          TextButton.icon(
                            onPressed: _startAddingStop,
                            icon: const Icon(Icons.add),
                            label: const Text("Add Stop"),
                          ),
                      ],
                    ),
                    const SizedBox(height: 16),
              
                    // Main content area
                    if (_rideStops.isNotEmpty) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                "Current Stops (${_rideStops.length})",
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                  fontSize: 14,
                                ),
                              ),
                              if (!_isAddingStop)
                                Text(
                                  "Drag to reorder",
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Theme.of(context).brightness == Brightness.dark 
                                        ? Colors.grey[400] 
                                        : Colors.grey[600],
                                  ),
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          ReorderableListView.builder(
                            shrinkWrap: true,
                            physics: const NeverScrollableScrollPhysics(),
                            onReorder: _isAddingStop ? (oldIndex, newIndex) {} : _reorderStops,
                            itemCount: _rideStops.length,
                            itemBuilder: (context, index) {
                              final stop = _rideStops[index];
                              return Card(
                                key: ValueKey('stop_${index}_${stop.title.hashCode}'),
                                margin: const EdgeInsets.only(bottom: 8),
                                elevation: 2,
                                color: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey[800] 
                                    : Colors.white,
                                child: ListTile(
                                  leading: CircleAvatar(
                                    backgroundColor: Theme.of(context).primaryColor,
                                    child: Text(
                                      '${index + 1}',
                                      style: const TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  title: Text(
                                    stop.title,
                                    style: TextStyle(
                                      fontWeight: FontWeight.w500,
                                      color: Theme.of(context).brightness == Brightness.dark 
                                          ? Colors.white 
                                          : Colors.black,
                                    ),
                                  ),
                                  subtitle: Text(
                                    stop.currentAddress,
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                    style: TextStyle(
                                      color: Theme.of(context).brightness == Brightness.dark 
                                          ? Colors.grey[300] 
                                          : Colors.grey[600],
                                    ),
                                  ),
                                  trailing: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      if (!_isAddingStop)
                                        IconButton(
                                          icon: const Icon(Icons.delete, color: Colors.red),
                                          onPressed: () => _removeStop(index),
                                          tooltip: "Delete stop",
                                        ),
                                      if (!_isAddingStop)
                                        Icon(
                                          Icons.drag_handle, 
                                          color: Theme.of(context).brightness == Brightness.dark 
                                              ? Colors.grey[400] 
                                              : Colors.grey[600],
                                        ),
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                          const SizedBox(height: 24),
                        ],
                      ),
                    ],
                                        
                    // Add stop form - shown when adding or when no stops exist
                    if (_isAddingStop || _rideStops.isEmpty) ...[
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (_isAddingStop) ...[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Add New Stop",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                TextButton(
                                  onPressed: _cancelAddingStop,
                                  child: const Text("Cancel"),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),
                          ] else if (_rideStops.isNotEmpty) ...[
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                const Text(
                                  "Add Another Stop",
                                  style: TextStyle(
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                                if (_rideStops.length < Constants.maxStopsInARide)
                                  TextButton.icon(
                                    onPressed: _startAddingStop,
                                    icon: const Icon(Icons.add),
                                    label: const Text("Add Stop"),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),
                          ],
                          
                          const Text(
                            "Search for a place",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 10),
                          
                          TextFormField(
                            controller: _searchController,
                            focusNode: _focusNode,
                            style: TextStyle(
                              color: Theme.of(context).brightness == Brightness.dark 
                                  ? Colors.white 
                                  : Colors.black,
                            ),
                            decoration: InputDecoration(
                              border: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              enabledBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Theme.of(context).brightness == Brightness.dark 
                                      ? Colors.grey[600]! 
                                      : Colors.grey[400]!,
                                ),
                              ),
                              focusedBorder: OutlineInputBorder(
                                borderSide: BorderSide(
                                  color: Theme.of(context).primaryColor,
                                ),
                              ),
                              fillColor: Theme.of(context).brightness == Brightness.dark 
                                  ? Colors.grey[800] 
                                  : Colors.white,
                              filled: true,
                              suffixIcon: _isSearching
                                  ? const SizedBox(
                                      width: 20,
                                      child: RooLoader(
                                        size: 20,
                                      ),
                                    )
                                  : Icon(
                                      Icons.search,
                                      color: Theme.of(context).brightness == Brightness.dark 
                                          ? Colors.grey[400] 
                                          : Colors.grey[600],
                                    ),
                            ),
                            onChanged: (value) {
                              // Initialize current stop if needed
                              _initializeCurrentStopIfNeeded();
                              
                              if (value.length >= 3) {
                                _onSearching(context, value);
                              } else {
                                setState(() {
                                  _addressList = [];
                                });
                              }
                            },
                          ),
                          
                          const SizedBox(height: 12),
                          
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              TextButton(
                                onPressed: _pickFromMap,
                                child: const Row(
                                  children: [
                                    Text("From map"),
                                    Icon(Icons.location_on)
                                  ],
                                ),
                              ),
                              TextButton(
                                onPressed: _pickFromSavedPlaces,
                                child: const Row(
                                  children: [Text("Favorites"), Icon(Icons.flag)],
                                ),
                              ),
                            ],
                          ),
                          
                          const SizedBox(height: 12),
                          
                          // Address suggestions dropdown
                          if (_addressList.isNotEmpty)
                            Container(
                              constraints: const BoxConstraints(maxHeight: 200),
                              child: Card(
                                color: Theme.of(context).brightness == Brightness.dark 
                                    ? Colors.grey[800] 
                                    : Colors.white,
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: _addressList.length,
                                  itemBuilder: (context, index) {
                                    MapBoxSuggestion data = _addressList[index];
                                    return ListTile(
                                      onTap: () async {
                                        _onSelectPickup(data: data);
                                      },
                                      trailing: const Icon(Icons.my_location_outlined),
                                      title: Text(
                                        data.name.toString(),
                                        style: TextStyle(
                                          color: Theme.of(context).brightness == Brightness.dark 
                                              ? Colors.white 
                                              : Colors.black,
                                        ),
                                      ),
                                      subtitle: Text(
                                        data.fullAddress.toString(),
                                        style: TextStyle(
                                          color: Theme.of(context).brightness == Brightness.dark 
                                              ? Colors.grey[300] 
                                              : Colors.grey[600],
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          
                          // Show selected location info
                          if (_currentStop != null && _currentStop!.latitude != 0.0) ...[
                            const SizedBox(height: 16),
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
                                borderRadius: BorderRadius.circular(8),
                                border: Border.all(
                                  color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.location_on,
                                    color: Theme.of(context).primaryColor,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          "Selected Location:",
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Theme.of(context).brightness == Brightness.dark 
                                                ? Colors.grey[300] 
                                                : Colors.grey[600],
                                          ),
                                        ),
                                        Text(
                                          _currentStop!.title,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                          
                          // Show "Add Stop" button when location is selected
                          if (_isAddingStop && _currentStop != null && _currentStop!.latitude != 0.0) ...[
                            const SizedBox(height: 20),
                            Center(
                              child: AppButtonWidget(
                                fullWidth: false,
                                text: "Add Stop",
                                onTap: _onRideStopSave,
                              ),
                            ),
                          ],
                          
                          // Show "Add Stop" button when not in adding mode but no stops exist
                          if (!_isAddingStop && _rideStops.isEmpty && _currentStop != null && _currentStop!.latitude != 0.0) ...[
                            const SizedBox(height: 20),
                            Center(
                              child: AppButtonWidget(
                                fullWidth: false,
                                text: "Add Stop",
                                onTap: _onRideStopSave,
                              ),
                            ),
                          ],
                          
                          const SizedBox(height: 20),
                        ],
                      ),
                    ],
              
                    // Save all stops button
                    if (_rideStops.isNotEmpty && !_isAddingStop)
                      Center(
                        child: AppButtonWidget(
                          fullWidth: false,
                          text: "Save All Stops",
                          onTap: _saveAllStops,
                        ),
                      ),
                  ],
                ),
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Future<void> _saveAllStops() async {
    if (_rideStops.isEmpty) {
      toast("No stops to save");
      return;
    }

    // Show confirmation dialog
    bool? shouldSave = await showAppDialog(
      dialogType: AppDialogType.confirmation,
      title: "Save ${_rideStops.length} stop${_rideStops.length > 1 ? 's' : ''}? This will save all stops in the current order. Continue?",
      onAccept: () => Navigator.of(context).pop(true),
      onCancel: () => Navigator.of(context).pop(false),
    );

    if (shouldSave != true) {
      return;
    }

    showAppActivity();
    
    try {
      // Save all stops in order
      List<RideStop> savedStops = [];
      
      for (int i = 0; i < _rideStops.length; i++) {
        final stop = _rideStops[i];
        
        // Update current location for each stop
        nlp.Location location = nlp.Location();
        LocationData locationData = await location.getLocation();
        
        if (locationData.latitude != null) {
          stop.currentLatitude = locationData.latitude!;
          stop.currentLongitude = locationData.longitude!;

          List<Placemark> places = await placemarkFromCoordinates(
              locationData.latitude!, locationData.longitude!);

          if (places.isNotEmpty) {
            Placemark place = places[0];
            stop.currentAddress =
                "${place.name ?? place.subThoroughfare}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}";
          }
        }

        var result = await ManageRideStopsLogic.add(stop);
        if (result == null) {
          toast(Globals.language.errorMsg);
          hideAppActivity();
          return;
        } else if (result.status == false) {
          if ((result.data?.isThePriceIncreasedAfterThisStop ?? false) == true) {
            if (result.data != null) {
              stop.payablePrice = result.data!.payablePrice;
            }
            await _askForPriceIncrease(result.message, stop);
          } else {
            toast(result.message);
            hideAppActivity();
            return;
          }
        } else if (result.status) {
          savedStops.add(result.data!);
        }
      }
      
      // Return all saved stops
      Navigator.of(context).pop(savedStops);
      toast("All stops saved successfully");
      
    } catch (e) {
      toast(Globals.language.errorMsg);
    }
    
    hideAppActivity();
  }

  Future<void> _onRideStopSave() async {
    _initializeCurrentStopIfNeeded();
    
    if (_currentStop!.latitude == 0.0) {
      toast("Please enter a valid location");
      return;
    }
    
    hideKeyboard();
    showAppActivity();

    nlp.Location location = nlp.Location();

    LocationData locationData = await location.getLocation();
    if (locationData.latitude != null) {
      _currentStop!.currentLatitude = locationData.latitude!;
      _currentStop!.currentLongitude = locationData.longitude!;

      List<Placemark> places = await placemarkFromCoordinates(
          locationData.latitude!, locationData.longitude!);

      if (places.isNotEmpty) {
        Placemark place = places[0];
        _currentStop!.currentAddress =
            "${place.name ?? place.subThoroughfare}, ${place.subLocality}, ${place.locality}, ${place.administrativeArea} ${place.postalCode}, ${place.country}";
      }
    }

    // Add the stop to the list
    setState(() {
      _currentStop!.id = _nextStopKey--; // Assign unique negative ID
      _rideStops.add(_currentStop!);
      _isAddingStop = false;
      _currentStop = null;
      _searchController.clear();
      _addressList.clear();
    });

    hideAppActivity();
    toast("Stop added to list");
    
    // If we haven't reached the limit, ask if user wants to add more
    if (_rideStops.length < Constants.maxStopsInARide) {
      bool? continueAdding = await showAppDialog(
        dialogType: AppDialogType.confirmation,
        title: "Stop added! Would you like to add another stop?",
        onAccept: () {
          Navigator.of(context).pop(true);
        },
        onCancel: () {
          Navigator.of(context).pop(false);
        },
      );
      
      if (continueAdding == true) {
        _startAddingStop();
      }
    }
  }

  Future<void> _askForPriceIncrease(String msg, RideStop stop) async {
    showAppDialog(
      barrierDismissible: false,
      onAccept: () {
        if ((stop.payablePrice ?? 0) > 0) {
          launchScreen(
            SelectPaymentForAdditionalCharges(
              payableAmount: stop.payablePrice!,
              rideId: widget.rideId,
              chargesType: AdditionalChargesType.stopsChange,
            ),
            // isNewTask: true,
          );
        } else {
          Navigator.of(context).pop();
          stop.isThePriceIncreasedAfterThisStop = false;
        }
      },
      dialogType: AppDialogType.confirmation,
      title: msg,
    );
  }

  Future<void> _onSearching(BuildContext context, String searchText) async {
    setState(() {
      _isSearching = true;
    });
    
    if (_debounceTimer != null && _debounceTimer!.isActive) {
      _debounceTimer!.cancel();
    }

    _debounceTimer = Timer(const Duration(milliseconds: 500), () async {
      _getSearchResults(searchText: searchText);
    });
  }

  _getSearchResults({required String searchText}) {
    searchWithMapBox(
      search: searchText,
      sessionToken: _sessionToken,
      proximity: _currentLocation,
    ).then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }

      if (_searchController.text.length >= 3) {
        _addressList = [];
        for (var element in value.suggestions) {
          if (element.fullAddress.isNotEmpty) {
            _addressList.add(element);
          }
        }
        setState(() {
          _isSearching = false;
        });
      }
    });
  }

  _onSelectPickup({required MapBoxSuggestion data}) async {
    _initializeCurrentStopIfNeeded();
    
    showAppActivity();
    await getMapBoxLocation(
            mapBoxId: data.mapBoxId, sessionToken: _sessionToken)
        .then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
        return;
      }
      _currentStop!.latitude = value.latitude;
      _currentStop!.longitude = value.longitude;
      _currentStop!.title = data.name.toString();
      _searchController.text = _currentStop!.title;
      setState(() {
        _addressList = [];
      });
    });
    hideKeyboard();
    hideAppActivity();
  }

  Future<void> _pickFromMap() async {
    _initializeCurrentStopIfNeeded();
    
    hideKeyboard();
    setState(() {
      _searchController.text = "";
      _addressList = [];
    });
    MapBoxLocationModel? selectedPlace =
        await launchScreen(const MapPickupScreen());
    if (selectedPlace != null) {
      _currentStop!.latitude = selectedPlace.point.latitude;
      _currentStop!.longitude = selectedPlace.point.longitude;
      _currentStop!.title = selectedPlace.address.toString();
      setState(() {
        _searchController.text = _currentStop!.title;
      });
    }
  }

  Future<void> _pickFromSavedPlaces() async {
    _initializeCurrentStopIfNeeded();
    
    hideKeyboard();
    setState(() {
      _searchController.text = "";
      _addressList = [];
    });
    SavedPlace? savedPlace =
        await launchScreen(const SavedPlacesScreen(), context: context);
    if (savedPlace != null) {
      _currentStop!.latitude = double.parse(savedPlace.latitude);
      _currentStop!.longitude = double.parse(savedPlace.longitude);
      _currentStop!.title = savedPlace.title;
      setState(() {
        _searchController.text = _currentStop!.title;
      });
    }
  }
}

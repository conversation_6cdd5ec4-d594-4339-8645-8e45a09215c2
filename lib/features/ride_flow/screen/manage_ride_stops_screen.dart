import 'package:rider/app_exports.dart';
import 'package:rider/features/ride_flow/screen/add_ride_stop_screen.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:rider/global/models/ride_model.dart';

class ManageRideStopScreen extends StatefulWidget {
  final RideModel? currentRide;
  const ManageRideStopScreen({
    super.key,
    required this.currentRide,
  });

  @override
  State<ManageRideStopScreen> createState() => _ManageRideStopScreenState();
}

class _ManageRideStopScreenState extends State<ManageRideStopScreen> {
  final List<RideStop> _rideStops = [];
  bool _areThereChanges = false;
  @override
  void initState() {
    super.initState();
    if (widget.currentRide != null) {
      _rideStops.addAll(
        List.from(widget.currentRide!.onRideRequest!.rideStops ?? []),
      );
    }
  }

  @override
  dispose() {
    super.dispose();
    hideAppActivity();
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: false,
      child: Scaffold(
        appBar: const RoooAppbar(
          title: "Ride stops",
          hideBackButton: true,
        ),
        bottomNavigationBar: Padding(
          padding: const EdgeInsets.all(Layout.scaffoldBodyPadding),
          child: AppButtonWidget(
            fullWidth: false,
            text: "Back to ride",
            onTap: () {
              Navigator.of(context).pop(_areThereChanges);
            },
          ),
        ),
        body: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(
                Layout.scaffoldBodyPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // const Text(
                  //   "Add stop",
                  //   style: TextStyle(
                  //     fontWeight: FontWeight.bold,
                  //     fontSize: 20,
                  //   ),
                  // ),
                  // const SizedBox(
                  //   height: 20,
                  // ),
                  Expanded(
                    child: _getRideStopsView(),
                  ),
                  const SizedBox(
                    height: 20,
                  ),
                  Center(
                    child: AppButtonWidget(
                      fullWidth: false,
                      text: "Add new ",
                      onTap: _openAddStopScreen,
                    ),
                  )
                ],
              ),
            ),
            const ActivityIndicator(),
          ],
        ),
      ),
    );
  }

  Widget _getRideStopsView() {
    if (_rideStops.isEmpty) {
      return const Center(
        child: Text(
          "No stops added yet",
          style: TextStyle(
            fontSize: 18,
          ),
        ),
      );
    }
    return ListView.separated(
      shrinkWrap: true,
      separatorBuilder: (context, index) => height10,
      itemCount: _rideStops.length,
      itemBuilder: (BuildContext context, int index) {
        return RideStopWidget(
          rideStop: _rideStops[index],
          onDelete: (rideStop) {
            _onDeleteClick(rideStop);
          },
        );
      },
    );
  }

  Future<bool?> _confirmDelete() async {
    return await showAppDialog(
      dialogType: AppDialogType.confirmation,
      onAccept: () {
        Navigator.of(context).pop(true);
      },
      onCancel: () {
        Navigator.of(context).pop(false);
      },
      title: "Are you sure you want to delete this stop?",
    );
  }

  _onDeleteClick(RideStop rideStop) async {
    bool? result = await _confirmDelete();
    if (result == true) {
      showAppActivity();
      var response = await deleteRideStop(
          stopId: rideStop.id, rideId: widget.currentRide!.onRideRequest!.id!);
      if (!response.status) {
        toast(Globals.language.errorMsg);
      } else {
        _areThereChanges = true;
        _rideStops.remove(rideStop);
      }
      setState(() {
        hideAppActivity();
      });
    }
  }

  Future<void> _openAddStopScreen() async {
    if (_rideStops.length == Constants.maxStopsInARide) {
      return toast("Only 3 stops are allowed in a ride");
    }
    
    List<RideStop>? result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => AddRideStopScreen(
          rideId: widget.currentRide!.onRideRequest!.id!,
          existingStops: _rideStops,
        ),
      ),
    );
    
    if (result != null && result.isNotEmpty) {
      _areThereChanges = true;
      setState(() {
        _rideStops.clear();
        _rideStops.addAll(result);
      });
    }
  }
}

class RideStopWidget extends StatelessWidget {
  final RideStop rideStop;
  final void Function(RideStop) onDelete;
  const RideStopWidget(
      {super.key, required this.rideStop, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return ListTile(
      tileColor: Colors.grey.shade200,
      onTap: (rideStop.isArrived ?? false)
          ? null
          : () {
              onDelete(rideStop);
            },
      title: Text(
        rideStop.title,
        maxLines: 2,
        overflow: TextOverflow.ellipsis,
        style: const TextStyle(
          color: Colors.black,
        ),
      ),
      trailing: (rideStop.isArrived ?? false)
          ? const Icon(
              Icons.check,
              color: Colors.green,
            )
          : const Icon(
              Icons.delete,
              color: Colors.red,
            ),
    );
  }
}

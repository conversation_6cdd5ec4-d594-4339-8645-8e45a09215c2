import 'package:rider/app_exports.dart';

abstract class ManageRideStopsLogic {
  static Future<AddRideStopResponse?> add(RideStop rideStop) async {
    return await addRideStop(request: rideStop);
  }

  static ApiBaseResponse delete(RideStop rideStop) {
    return ApiBaseResponse(
        status: false, message: "Not implemented", data: null);
  }
}

class RideStop {
  double latitude;
  double longitude;
  double currentLatitude;
  double currentLongitude;
  String currentAddress;
  String title;
  int? rideId;
  int id;
  bool? isThePriceIncreasedAfterThisStop;
  bool? isArrived;
  num? payablePrice;
  num? debitWallet;
  num? paymentCardId;
  RideStop(
      {required this.latitude,
      required this.longitude,
      required this.currentLatitude,
      required this.currentLongitude,
      required this.currentAddress,
      required this.title,
      required this.rideId,
      required this.id,
      required this.isThePriceIncreasedAfterThisStop,
      required this.isArrived,
      required this.payablePrice,
      required this.debitWallet,
      required this.paymentCardId,
      });

  factory RideStop.fromJson(Map<String, dynamic> json) {
    return RideStop(
      latitude: json['stop_lat'] as double,
      longitude: json['stop_lng'] as double,
      currentLatitude: json['current_lat'] as double,
      currentLongitude: json['current_lng'] as double,
      currentAddress: json['current_address'] as String,
      title: json['title'] as String,
      rideId: json['ride_request_id'] as int?,
      id: json['id'] as int,
      isThePriceIncreasedAfterThisStop: json['is_flag'] as bool?,
      isArrived: json['is_arrived'] as bool?,
      payablePrice: json['payable_amount'] as num?,
      debitWallet: json['debitwallet'] as num?,
      paymentCardId: json['payment_card_id'] as num?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'stop_lat': latitude,
      'stop_lng': longitude,
      'current_lat': currentLatitude,
      'current_lng': currentLongitude,
      'current_address': currentAddress,
      'title': title,
      'ride_request_id': rideId,
      "is_flag": isThePriceIncreasedAfterThisStop,
      "payable_amount": payablePrice,
      "is_arrived": isArrived,
      "debitwallet": debitWallet,
      "payment_card_id": paymentCardId,
    };
  }
}

class AddRideStopResponse extends ApiBaseResponse<RideStop> {
  AddRideStopResponse(
      {required super.status, required super.message, required super.data});

  factory AddRideStopResponse.fromJson(Map<String, dynamic> json) {
    return AddRideStopResponse(
      status: json['status'] as bool,
      message: json['message'] as String,
      data: json['data'] != null ? RideStop.fromJson(json['data']) : null,
    );
  }
}

class StopEstimatePriceResponse extends ApiBaseResponse<StopRemainingPayment> {
  StopEstimatePriceResponse(
      {required super.status, required super.message, required super.data});

  factory StopEstimatePriceResponse.fromJson(Map<String, dynamic> json) {
    return StopEstimatePriceResponse(
      status: json['status'] as bool,
      message: json['message'] as String,
      data: json['data'] != null
          ? StopRemainingPayment.fromJson(json['data'])
          : null,
    );
  }
}

class StopRemainingPayment {
  num amount;
  StopRemainingPayment({required this.amount});

  factory StopRemainingPayment.fromJson(Map<String, dynamic> json) {
    return StopRemainingPayment(
      amount: json['remaining_amount'],
    );
  }
}

class ApiBaseResponse<T> {
  bool status;
  String message;
  T? data;

  ApiBaseResponse(
      {required this.status, required this.message, required this.data});

  factory ApiBaseResponse.fromJson(Map<String, dynamic> json) {
    return ApiBaseResponse(
      status: json['status'] as bool,
      message: json['message'] as String,
      data: null,
    );
  }
}

class RideDestination {
  double latitude;
  double longitude;
  double currentLatitude;
  double currentLongitude;
  String currentAddress;
  String title;
  int? rideId;
  int id;
  bool? isThePriceIncreasedAfterThisStop;
  num? payablePrice;

  RideDestination({
    required this.latitude,
    required this.longitude,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.currentAddress,
    required this.title,
    required this.rideId,
    required this.id,
    required this.isThePriceIncreasedAfterThisStop,
    this.payablePrice,
  });

  factory RideDestination.fromJson(Map<String, dynamic> json) {
    return RideDestination(
      latitude: json['stop_lat'] as double,
      longitude: json['stop_lng'] as double,
      currentLatitude: json['current_lat'] as double,
      currentLongitude: json['current_lng'] as double,
      currentAddress: json['current_address'] as String,
      title: json['title'] as String,
      rideId: json['ride_request_id'] as int?,
      id: json['id'] as int,
      isThePriceIncreasedAfterThisStop: json['is_flag'] as bool?,
      payablePrice: json['payable_amount'] as num?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'current_lat': currentLatitude,
      'current_lng': currentLongitude,
      'current_address': currentAddress,
      'title': title,
      'ride_request_id': rideId,
      "is_flag": isThePriceIncreasedAfterThisStop,
      "payable_amount": payablePrice,
    };
  }
}

class RidePickup {
  double latitude;
  double longitude;
  double currentLatitude;
  double currentLongitude;
  String currentAddress;
  String title;
  int? rideId;
  int id;
  // bool? isThePriceIncreasedAfterThisStop;
  // num? payablePrice;

  RidePickup({
    required this.latitude,
    required this.longitude,
    required this.currentLatitude,
    required this.currentLongitude,
    required this.currentAddress,
    required this.title,
    required this.rideId,
    required this.id,
    // required this.isThePriceIncreasedAfterThisStop,
    // this.payablePrice,
  });

  factory RidePickup.fromJson(Map<String, dynamic> json) {
    return RidePickup(
      latitude: json['pickup_lat'] as double,
      longitude: json['pickup_lng'] as double,
      currentLatitude: json['current_lat'] as double,
      currentLongitude: json['current_lng'] as double,
      currentAddress: json['current_address'] as String,
      title: json['title'] as String,
      rideId: json['ride_request_id'] as int?,
      id: json['id'] as int,
      // isThePriceIncreasedAfterThisStop: json['is_flag'] as bool?,
      // payablePrice: json['payable_amount'] as num?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'pickup_lat': latitude,
      'pickup_lng': longitude,
      'current_lat': currentLatitude,
      'current_lng': currentLongitude,
      'current_address': currentAddress,
      'title': title,
      'ride_request_id': rideId,
      // "is_flag": isThePriceIncreasedAfterThisStop,
      // "payable_amount": payablePrice,
    };
  }
}

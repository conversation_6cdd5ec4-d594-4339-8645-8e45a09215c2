import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/booking/models/save_ride_model.dart';
import 'package:rider/features/ride_flow/screen/current_ride_screen.dart';
import 'package:rider/services/stripe_service.dart';

class ApplePaymentScreen extends StatefulWidget {
  final PaymentItem paymentItem;
  final num payableAmount;
  final num? deductionAmount;
  final SelectServiceModel priceData;
  final dynamic otherRiderData;
  final bool isOTPEnable;
  final Position sourceLocation;
  final Position destinationLocation;
  final String startAddress;
  final String endAddress;
  final DateTime? scheduledRideTime;
  final bool isPoolingRide;
  final int personCount;
  final bool isBusinessRide;
  final bool is_peak;
  final String ?couponCode;
  final num ?couponDiscount;

  final CouponData? couponData;

  const ApplePaymentScreen({
    super.key,
    required this.paymentItem,
    required this.payableAmount,
    required this.deductionAmount,
    required this.priceData,
    required this.is_peak,
    required this.sourceLocation,
    required this.destinationLocation,
    required this.startAddress,
    required this.endAddress,
    required this.otherRiderData,
    required this.isOTPEnable,
    required this.scheduledRideTime,
     this.couponData,
    required this.isPoolingRide,
    required this.personCount,
    required this.isBusinessRide,
     this.couponCode,
     this.couponDiscount,
  });

  @override
  State<ApplePaymentScreen> createState() => _ApplePaymentScreenState();
}

class _ApplePaymentScreenState extends State<ApplePaymentScreen> {
  String _holdPaymentId = "";

  late final Pay _payClient;

  @override
  void initState() {
    showAppActivity();

    super.initState();

    _startPayment();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: RoooAppbar(title: "Pay with Apple pay"),
      body: Stack(
        children: [
          ActivityIndicator(),
        ],
      ),
    );
  }

  String getApplePayData() {
    return jsonEncode({
      "provider": "apple_pay",
      "data": {
        "merchantIdentifier": "merchant.au.rooo",
        "merchantCapabilities": ["3DS"],
        "supportedNetworks": ["amex", "visa", "discover", "masterCard"],
        "countryCode": "AU",
        "currencyCode": "AUD",
      },
    });
  }

  Future<void> _startPayment() async {
    try {
      _payClient = Pay(
        {
          PayProvider.apple_pay: PaymentConfiguration.fromJsonString(
            getApplePayData(),
          ),
        },
      );
      bool canPay = await _payClient.userCanPay(PayProvider.apple_pay);
      if (!canPay) {
        toast("Apple pay is not supported on this device");
        closeScreen();
        return;
      }

      var response = await StripeService.getPaymentTypeList();
      if (!response) {
        toast(Globals.language.errorMsg);
        return closeScreen();
      }
      if (!mounted) {
        return;
      }

      final result = await _payClient.showPaymentSelector(
        PayProvider.apple_pay,
        [widget.paymentItem],
      );
      await _onApplePayResult(result);
    } on PlatformException catch (e) {
      if (e.code.toString() == "paymentCanceled") {
        closeScreen();
        toast("You cancelled the payment");
      }
    } catch (e) {
      closeScreen();
      toast(Globals.language.errorMsg);
    }
  }

  void closeScreen() {
    Navigator.of(context).pop();
  }

  Future<void> _onApplePayResult(Map<String, dynamic> result) async {
    var response = await StripeService.getPaymentObject(
      payableAmount: num.parse(widget.paymentItem.amount),
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: PaymentType.wallet,
      walletDeductionAmount: widget.deductionAmount,
    );

    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    final token = await Stripe.instance.createApplePayToken(result);

    final params = PaymentMethodParams.cardFromToken(
      paymentMethodData: PaymentMethodDataCardFromToken(
        token: token.id,
      ),
    );

    await Stripe.instance.confirmPayment(
      paymentIntentClientSecret: response.clientSecret!,
      data: params,
    );
    _holdPaymentId = response.id!;

    await _saveRide();
  }

  Future<void> _saveRide() async {
    showAppActivity();
    SaveRideModel request = SaveRideModel(
      couponData: widget.couponData,
      peak_ride: widget.is_peak,
      isRideForOther: widget.otherRiderData != null,
      otherRiderData: widget.otherRiderData,
      serviceId: widget.priceData.id.toString(),
      isOTPSharingRequired: widget.isOTPEnable,
      isPoolingRide: widget.isPoolingRide,
      isBusinessRide: widget.isBusinessRide,
      personCount: widget.personCount,
      debitWallet: widget.deductionAmount,
      baseFare: widget.priceData.baseFare,
      baseDistance: widget.priceData.baseDistance,
      extraCharges: widget.priceData.extraCharges,
      extraChargesAmount: widget.priceData.extraChargesAmount,
      isScheduledRide: widget.scheduledRideTime != null,
      scheduledDate: widget.scheduledRideTime,
      minimumFare: widget.priceData.minimumFare,
      perDistance: widget.priceData.perDistance,
      perDistanceCharge: widget.priceData.perDistanceCharge,
      perMinuteDrive: widget.priceData.perMinuteDrive,
      perMinuteDriveCharge: widget.priceData.perMinuteDriveCharge,
      perMinuteWaiting: null,
      perMinuteWaitingCharge: null,
      subtotal: widget.priceData.subtotal,
      waitingTimeLimit: widget.priceData.waitingTimeLimit,
      totalAmount: widget.priceData.totalAmount,
      couponCode: widget.couponCode,
      couponDiscount: widget.couponDiscount,
      status: RideStatus.newRideRequested,
      holdPaymentId: _holdPaymentId,
      paymentCardId: null,
      riderId: Globals.user.id.toString(),
      startLatitude: widget.sourceLocation.lat.toString(),
      startLongitude: widget.sourceLocation.lng.toString(),
      startAddress: widget.startAddress,
      endLatitude: widget.destinationLocation.lat.toString(),
      endLongitude: widget.destinationLocation.lng.toString(),
      endAddress: widget.endAddress,
      paymentType: 'pre-authorized payment',
      tax: widget.priceData.tax,
      finalAmount: widget.payableAmount,
      distance: widget.priceData.distance,
      isMinimumAmountApplies: widget.priceData.isMinimumAmountApplies,
      stripe: widget.priceData.stripe,
        airportCharges: widget.priceData.airportCharges,
      duration: widget.priceData.duration,
    );

    LDBaseResponse response = await saveRideRequest(request.toJson());
    if (!(response.status ?? false)) {
      toast(response.message);
    } else {
      if (widget.scheduledRideTime != null) {
        showAppDialog(
          onAccept: () {
            launchScreen(
              const DashboardWrapperScreen(),
              isNewTask: true,
            );
          },
          dialogType: AppDialogType.info,
          title:
              "Your ride is scheduled for ${widget.scheduledRideTime!.hour}:${widget.scheduledRideTime!.minute}.\n\nWe will inform you when your ride is assigned to a driver.",
        );
        return;
      }

      /* Goto ride screen */

      launchScreen(
        const CurrentRideScreen(
          currentRide: null,
        ),
      );
    }
  }
}

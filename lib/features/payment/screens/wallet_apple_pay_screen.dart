import 'package:flutter/services.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'package:pay/pay.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/services/stripe_service.dart';

class WalletApplePayScreen extends StatefulWidget {
  final PaymentItem paymentItem;

  const WalletApplePayScreen({
    super.key,
    required this.paymentItem,
  });

  @override
  State<WalletApplePayScreen> createState() => _WalletApplePayScreenState();
}

class _WalletApplePayScreenState extends State<WalletApplePayScreen> {
  // String _holdPaymentId = "";

  late final Pay _payClient;

  @override
  void initState() {
    showAppActivity();

    super.initState();

    _startPayment();
  }

  @override
  dispose() {
    hideAppActivity();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      appBar: RoooAppbar(title: "Pay with Apple pay"),
      body: Stack(
        children: [
          ActivityIndicator(),
        ],
      ),
    );
  }

  String getApplePayData() {
    return jsonEncode({
      "provider": "apple_pay",
      "data": {
        "merchantIdentifier": "merchant.au.rooo",
        "merchantCapabilities": ["3DS"],
        "supportedNetworks": ["amex", "visa", "discover", "masterCard"],
        "countryCode": "AU",
        "currencyCode": "AUD",
      },
    });
  }

  Future<void> _startPayment() async {
    _payClient = Pay(
      {
        PayProvider.apple_pay: PaymentConfiguration.fromJsonString(
          getApplePayData(),
        ),
      },
    );
    bool canPay = await _payClient.userCanPay(PayProvider.apple_pay);
    if (!canPay) {
      toast("Apple pay is not supported on this device");
      closeScreen();
      return;
    }

    try {
      var response = await StripeService.getPaymentTypeList();
      if (!response) {
        toast(Globals.language.errorMsg);
        return closeScreen();
      }
      if (!mounted) {
        return;
      }

      final result = await _payClient.showPaymentSelector(
        PayProvider.apple_pay,
        [
          widget.paymentItem,
        ],
      );
      await _onApplePayResult(result);
    } on PlatformException catch (e) {
      if (e.code.toString() == "paymentCanceled") {
        closeScreen();
        toast("You cancelled the payment");
      }
    } catch (e) {
      closeScreen();
      toast(Globals.language.errorMsg);
    }
  }

  void closeScreen({bool? isDone}) {
    Navigator.of(context).pop();
    if (isDone == true) {
      Navigator.of(context).pop(true);
    }
  }

  Future<void> _onApplePayResult(Map<String, dynamic> result) async {
    var response = await StripeService.getWalletPaymentObject(
      payableAmount: int.parse(widget.paymentItem.amount),
      customerId: Globals.user.stripeCustomerId,
      userId: Globals.user.id.toString(),
      paymentType: PaymentType.wallet,
    );

    if (response == null) {
      toast(Globals.language.errorMsg);
      hideAppActivity();
      return;
    }

    final token = await Stripe.instance.createApplePayToken(result);

    final params = PaymentMethodParams.cardFromToken(
      paymentMethodData: PaymentMethodDataCardFromToken(
        token: token.id,
      ),
    );

    await Stripe.instance.confirmPayment(
      paymentIntentClientSecret: response.clientSecret!,
      data: params,
    );
    // _holdPaymentId = response.id!;
    closeScreen(isDone: true);

    // await _savePayment();
  }
}

import 'dart:math' as math;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mapbox;
import 'models/annotation_managers.dart';

class MapBoxService {
  static Future<mapbox.PointAnnotation> createMarker({
    required mapbox.PointAnnotationManager manager,
    required mapbox.Position position,
    required Uint8List image,
    double iconSize = 1.0,
  }) async {
    return await manager.create(
      mapbox.PointAnnotationOptions(
        geometry: mapbox.Point(coordinates: position),
        image: image,
        iconSize: iconSize,
      ),
    );
  }

  static Future<mapbox.PointAnnotation> createMarkerFromAsset({
    required mapbox.PointAnnotationManager manager,
    required mapbox.Position position,
    required String assetPath,
    double iconSize = 0.5,
  }) async {
    final ByteData bytes = await rootBundle.load(assetPath);
    final Uint8List list = bytes.buffer.asUint8List();
    return await createMarker(
      manager: manager,
      position: position,
      image: list,
      iconSize: iconSize,
    );
  }

  static Future<void> updateAnnotation({
    required dynamic manager,
    required dynamic annotation,
  }) async {
    if (manager != null && annotation != null) {
      await manager.update(annotation);
    }
  }

  static Future<mapbox.PolylineAnnotation> createRoute({
    required mapbox.PolylineAnnotationManager manager,
    required List<mapbox.Position> coordinates,
    Color color = Colors.blue,
    double lineWidth = 4.0,
    double borderWidth = 4.0,
    Color borderColor = Colors.black,
  }) async {
    return await manager.create(
      mapbox.PolylineAnnotationOptions(
        lineOpacity: 1,
        lineWidth: lineWidth,
        lineBorderWidth: borderWidth,
        lineBorderColor: borderColor.value,
        lineColor: color.value,
        lineJoin: mapbox.LineJoin.ROUND,
        geometry: mapbox.LineString(coordinates: coordinates),
      ),
    );
  }

  static Future<mapbox.CircleAnnotation> createCircle({
    required mapbox.CircleAnnotationManager manager,
    required mapbox.Position center,
    double radius = 5.0,
    Color color = Colors.red,
    double opacity = 0.2,
    double strokeWidth = 2.0,
    Color strokeColor = Colors.red,
  }) async {
    return await manager.create(
      mapbox.CircleAnnotationOptions(
        geometry: mapbox.Point(coordinates: center),
        circleRadius: radius,
        circleColor: color.value,
        circleOpacity: opacity,
        circleStrokeWidth: strokeWidth,
        circleStrokeColor: strokeColor.value,
      ),
    );
  }

  static double calculateCircleRadius(double radiusInKm, double zoomLevel) {
    double baseZoom = 12.0;
    double scaleFactor = math.pow(2, baseZoom - zoomLevel).toDouble();
    double screenRadius = (radiusInKm * 1000) / scaleFactor;
    screenRadius = screenRadius / 2;
    if (screenRadius < 5) {
      screenRadius = 5;
    }
    return screenRadius;
  }

  static Future<void> flyToPosition({
    required mapbox.MapboxMap mapboxMap,
    required mapbox.Position position,
    double zoom = 15.0,
    double bearing = 0.0,
    double pitch = 0.0,
    Duration duration = const Duration(milliseconds: 1000),
  }) async {
    await mapboxMap.flyTo(
      mapbox.CameraOptions(
        center: mapbox.Point(coordinates: position),
        zoom: zoom,
        bearing: bearing,
        pitch: pitch,
      ),
      mapbox.MapAnimationOptions(
        duration: duration.inMilliseconds,
        startDelay: 0,
      ),
    );
  }

  static Future<AnnotationManagers> initializeAnnotationManagers(
      mapbox.MapboxMap mapboxMap) async {
    final polylineManager =
        await mapboxMap.annotations.createPolylineAnnotationManager();
    final circleManager =
        await mapboxMap.annotations.createCircleAnnotationManager();
    final pointManager =
        await mapboxMap.annotations.createPointAnnotationManager();

    return AnnotationManagers(
      pointManager: pointManager,
      polylineManager: polylineManager,
      circleManager: circleManager,
    );
  }

  static Future<void> clearAnnotations(dynamic manager) async {
    if (manager != null) {
      try {
        await manager.deleteAll();
      } catch (e) {
        // print(e);
      }
    }
  }
}

import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:rider/app_counters.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/features/booking/models/current_ride_response_model.dart';
import 'package:rider/features/care/models/care_details_response_model.dart';
import 'package:rider/features/coupon-list/models/coupon_list_model.dart';
import 'package:rider/features/payment/model/wallet_response_model.dart';
import 'package:rider/features/push_notification_prefs/models/push_notification_prefs_models.dart';
import 'package:rider/features/ride-settings/models/ride_related_settings.dart';
import 'package:rider/features/ride_flow/screen/driver_estimated_time.dart';
import 'package:rider/features/ride_flow/screen/manage_ride_stops_logic.dart';
import 'package:rider/global/models/status_message_model.dart';
import 'package:rider/model/app_setting_model.dart';
import 'package:rider/screens/delete_account_screen.dart';
import 'package:rider/services/ads_service.dart';
import 'package:rider/services/zego_voice_call_service.dart';
import '../screens/CreateRideHelpTabScreen.dart';

Future<RegisterResponse> signUpApi(Map<String, dynamic> request) async {
  Response response = await buildHttpResponse('register',
      request: request, method: HttpMethod.post);

  var json = jsonDecode(response.body);
  try {
    return RegisterResponse.fromJson(json);
  } catch (e) {
    return RegisterResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<MultipartRequest> getMultiPartRequest(String endPoint,
    {String? baseUrl}) async {
  String url = baseUrl ?? buildBaseUrl(endPoint).toString();
  return MultipartRequest('POST', Uri.parse(url));
}

Future<StreamedResponse?> sendMultiPartRequest(
  MultipartRequest multiPartRequest,
) async {
  multiPartRequest.headers.addAll(buildHeaderTokens());
  try {
    return await chuckerHttpClient.send(multiPartRequest);
  } catch (e) {
    return null;
  }
}

Future sendMultiPartRequestNew(
  MultipartRequest multiPartRequest,
) async {
  String? result;
  multiPartRequest.headers.addAll(buildHeaderTokens());

  StreamedResponse response = await multiPartRequest.send();
  if (response.statusCode == 200) {
    Uint8List responseData = await response.stream.toBytes();
    result = String.fromCharCodes(responseData);
  }
  return result;
}

/// Profile Update
Future<StreamedResponse?> updateProfile({
  String? firstName,
  String? lastName,
  String? userEmail,
  String? address,
  String? contactNumber,
  String? gender,
  File? file,
  UserDetails? userDetails,
  int? regionId,
  int? provinceId,
}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');
  multiPartRequest.fields['id'] = Globals.user.id.toString();
  // multiPartRequest.fields['username'] = Globals.user.username!;
  multiPartRequest.fields['email'] = Globals.user.email!;
  multiPartRequest.fields['email'] = Globals.user.email!;

  multiPartRequest.fields['first_name'] = firstName.validate();
  multiPartRequest.fields['last_name'] = lastName.validate();
  multiPartRequest.fields['contact_number'] = contactNumber.validate();
  multiPartRequest.fields['address'] = address.validate();
  multiPartRequest.fields['gender'] = gender.validate();
  multiPartRequest.fields['region_id'] = regionId.toString();
  multiPartRequest.fields['province_id'] = provinceId.toString();

  if (file != null) {
    multiPartRequest.files
        .add(await MultipartFile.fromPath('profile_image', file.path));
  }

  return await sendMultiPartRequest(multiPartRequest);
}

Future<StreamedResponse?> updateProfileImage({
  required File file,
}) async {
  MultipartRequest multiPartRequest =
      await getMultiPartRequest('update-profile');

  multiPartRequest.fields['type'] = 'profile_picture';

  multiPartRequest.files
      .add(await MultipartFile.fromPath('profile_image', file.path));

  return await sendMultiPartRequest(multiPartRequest);
}

Future<UserDetailsResponse> getUserDetail({int? userId}) async {
  Response response =
      await buildHttpResponse('user-detail?id=$userId', method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return UserDetailsResponse.fromJson(json);
  } catch (e) {
    return UserDetailsResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<WalletListModel?> getWalletList({required int page}) async {
  Response response =
      await buildHttpResponse('wallet-list?page=$page', method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return WalletListModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<PaymentListResponseModel?> getPaymentList() async {
  Response response = await buildHttpResponse('payment-gateway-list?status=1',
      method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return PaymentListResponseModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<LDBaseResponse> saveWallet(Map request) async {
  Response response = await buildHttpResponse('save-wallet',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
    );
  }
}

Future<LDBaseResponse> saveSOS(Map request) async {
  Response response = await buildHttpResponse('save-sos',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
    );
  }
}

Future<ContactNumberListModel?> getSosList({int? regionId}) async {
  Response response = await buildHttpResponse(
      regionId != null ? 'sos-list?region_id=$regionId' : 'sos-list',
      method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return ContactNumberListModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<ContactNumberListModel?> deleteSosList({int? id}) async {
  Response response =
      await buildHttpResponse('sos-delete/$id', method: HttpMethod.post);
  var json = jsonDecode(response.body);
  try {
    return ContactNumberListModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<EstimatePriceResponse> estimatePriceList(Map request) async {
  Response response = await buildHttpResponse('estimate-price-time',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return EstimatePriceResponse.fromJson(json);
  } catch (e) {
    return EstimatePriceResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<LDBaseResponse> saveRideRequest(Map request) async {
  Response response = await buildHttpResponse('save-riderequest',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<LDBaseResponse> saveScheduleRideRequest(Map request) async {
  Response response = await buildHttpResponse('save-riderequest',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<AppSettingModel?> getAppSetting() async {
  Response response =
      await buildHttpResponse('admin-dashboard', method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return AppSettingModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<CurrentRideResponseModel> getCurrentRideRequest() async {
  Response response =
      await buildHttpResponse('current-riderequest', method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return CurrentRideResponseModel.fromJson(json);
  } catch (e) {
    return CurrentRideResponseModel(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<LDBaseResponse> rideRequestUpdate(
    {required Map request, int? rideId}) async {
  Response response = await buildHttpResponse('riderequest-update/$rideId',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<LDBaseResponse> ratingReview({required Map request}) async {
  Response response = await buildHttpResponse('save-ride-rating',
      method: HttpMethod.post, request: request);
  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<LDBaseResponse> adminNotify({required Map request}) async {
  Response response = await buildHttpResponse('admin-sos-notify',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<RiderListModel?> getRiderRequestList(
    {int? page,
    String? status,
    mp.Position? sourceLatLog,
    int? riderId}) async {
  if (sourceLatLog != null) {
    Response response = await buildHttpResponse(
        'riderequest-list?page=$page&rider_id=$riderId',
        method: HttpMethod.get);

    var json = jsonDecode(response.body);
    try {
      return RiderListModel.fromJson(json);
    } catch (e) {
      return null;
    }
  } else {
    Response response = await buildHttpResponse(
        status != null
            ? 'riderequest-list?page=$page&status=$status&rider_id=$riderId'
            : 'riderequest-list?page=$page&rider_id=$riderId',
        method: HttpMethod.get);

    var json = jsonDecode(response.body);
    try {
      return RiderListModel.fromJson(json);
    } catch (e) {
      return null;
    }
  }
}

Future<LDBaseResponse> saveComplain({required Map request}) async {
  Response response = await buildHttpResponse('save-complaint',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<LDBaseResponse> saveComplainWithImage({
  required Map request,
  File? imageFile,
}) async {
  try {
    if (imageFile != null) {
      // Use multipart request for image upload
      MultipartRequest multiPartRequest =
          await getMultiPartRequest('save-complaint');

      // Add all the fields from the request
      request.forEach((key, value) {
        multiPartRequest.fields[key.toString()] = value.toString();
      });

      // Verify file exists before adding
      if (await imageFile.exists()) {
        // Add the image file
        multiPartRequest.files
            .add(await MultipartFile.fromPath('attachment', imageFile.path));
      } else {
        log("Image file does not exist: ${imageFile.path}");
        return LDBaseResponse(status: false, message: "Image file not found");
      }

      StreamedResponse? response = await sendMultiPartRequest(multiPartRequest);
      if (response != null) {
        try {
          var responseData = await response.stream.bytesToString();
          var json = jsonDecode(responseData);
          return LDBaseResponse.fromJson(json);
        } catch (e) {
          log("Error parsing response: $e");
          return LDBaseResponse(
              status: false, message: "Error processing response");
        }
      } else {
        log("No response from server");
        return LDBaseResponse(
            status: false, message: "No response from server");
      }
    } else {
      // Use regular request if no image
      return await saveComplain(request: request);
    }
  } catch (e) {
    log("Error in saveComplainWithImage: $e");
    return LDBaseResponse(
        status: false, message: "Error uploading complaint: $e");
  }
}

Future<RideDetailModel?> rideDetail({required int? orderId}) async {
  Response response = await buildHttpResponse('riderequest-detail?id=$orderId',
      method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return RideDetailModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

/// Get Notification List
Future<NotificationListModel?> getNotification({required int page}) async {
  Response response = await buildHttpResponse('notification-list?page=$page',
      method: HttpMethod.post);
  var json = jsonDecode(response.body);
  try {
    return NotificationListModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<MapBoxSearchResponse?> searchWithMapBox({
  required String search,
  required String sessionToken,
  required mp.Position proximity,
}) async {
  Response response = await buildHttpResponse(
      'https://api.mapbox.com/search/searchbox/v1/suggest?q=${search}&session_token=$sessionToken&access_token=${AppCred.mapBoxPublicTokenKey}&proximity=${proximity.lng},${proximity.lat}',
      method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return MapBoxSearchResponse.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<MapBoxLocation?> getMapBoxLocation(
    {required String mapBoxId, required String sessionToken}) async {
  Response response = await buildHttpResponse(
      'https://api.mapbox.com/search/searchbox/v1/retrieve/${mapBoxId}?session_token=$sessionToken&access_token=${AppCred.mapBoxPublicTokenKey}&country=${Globals.riderRegionCode}',
      method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return MapBoxLocation.fromJson(json['features'][0]['properties']);
  } catch (e) {
    return null;
  }
}

// Future<GooglePlaceIdModel?> searchAddressRequestPlaceId(
//     {required String placeId}) async {
//   Response response = await buildHttpResponse(
//       "https://maps.googleapis.com/maps/api/place/details/json?place_id=$placeId&key=${Constants.googleMapAPIKey}",
//       method: HttpMethod.get);
//   var json = jsonDecode(response.body);
//   try {
//     return GooglePlaceIdModel.fromJson(json);
//   } catch (e) {
//     return null;
//   }
// }

Future<LDBaseResponse> deleteUser() async {
  Response response =
      await buildHttpResponse('delete-user-account', method: HttpMethod.post);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<LDBaseResponse> complaintComment({required Map request}) async {
  Response response = await buildHttpResponse('save-complaintcomment',
      method: HttpMethod.post, request: request);

  var json = jsonDecode(response.body);
  try {
    return LDBaseResponse.fromJson(json);
  } catch (e) {
    return LDBaseResponse(status: false, message: Globals.language.errorMsg);
  }
}

Future<ComplaintCommentModel?> complaintList(
    {required int complaintId, required int currentPage}) async {
  Response response = await buildHttpResponse(
      'complaintcomment-list?complaint_id=$complaintId&page=$currentPage',
      method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return ComplaintCommentModel.fromJson(json);
  } catch (e) {
    return null;
  }
}

Future<ApiBaseResponse> logoutApi() async {
  Response response = await buildHttpResponse(
    'logout?clear=player_id',
    method: HttpMethod.get,
  );

  if (response.statusCode == 401) {
    return ApiBaseResponse(status: true, message: "Done", data: null);
  }

  var json = jsonDecode(response.body);
  try {
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<UserDetailsResponse> getDriverDetail({int? userId}) async {
  Response response = await buildHttpResponse(
    'user-detail?id=$userId',
    method: HttpMethod.get,
  );

  var json = jsonDecode(response.body);
  try {
    return UserDetailsResponse.fromJson(json);
  } catch (e) {
    return UserDetailsResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<void> logOutSuccess() async {
  if (Globals.isLoggedOut == false) {
    Globals.isLoggedOut = true;
    Globals.isUserLoggedIn = false;
    Globals.sharedPrefs.clear();
    ZegoVoiceCallService.dispose();
    OneSignalService.dispose();
    LiveStream().emit("DARK_MODE_CHANGED");
    launchScreen(const LoginScreen(), isNewTask: true);
  }
}

Future<AppSettingsResponse> getAppSettingApi() async {
  Response response =
      await buildHttpResponse('appsetting', method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return AppSettingsResponse.fromMap(json);
  } catch (e) {
    return AppSettingsResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<LoginResponse> isUserExists({
  required String firstName,
  required String lastName,
  required String userType,
  required String? phoneNumber,
  required String? email,
  required String? googleId,
  required String? facebookId,
  required String? appleId,
  required String? microsoftId,
}) async {
  Response response = await buildHttpResponse('custom-login',
      request: {
        'contact_number': phoneNumber,
        'email': email,
        'google_id': googleId,
        'facebook_id': facebookId,
        'apple_id': appleId,
        'microsoft_id': microsoftId,
        'first_name': firstName,
        'last_name': lastName,
        "user_type": userType,
      },
      method: HttpMethod.post);

  try {
    var json = jsonDecode(response.body);
    return LoginResponse.fromJson(json);
  } catch (e) {
    return LoginResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<MobileLoginResponse> mobileLogin({
  required String userType,
  required String phoneNumber,
}) async {
  Response response = await buildHttpResponse('custom-login',
      request: {
        'contact_number': phoneNumber,
        "user_type": userType,
      },
      method: HttpMethod.post);

  try {
    var json = jsonDecode(response.body);
    return MobileLoginResponse.fromJson(json);
  } catch (e) {
    return MobileLoginResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<dynamic> getPrivacyPolicy() async {
  Response response =
      await buildHttpResponse('privacy-policy', method: HttpMethod.get);
  var json = jsonDecode(response.body);
  try {
    return json;
  } catch (e) {
    return null;
  }
}

Future<dynamic> getTAndC() async {
  Response response =
      await buildHttpResponse('term-conditions', method: HttpMethod.get);

  var json = jsonDecode(response.body);
  try {
    return json;
  } catch (e) {
    return null;
  }
}

Future<List<SavedPlace>?> getSavedPlaces() async {
  Response response = await buildHttpResponse('saved-places');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        List<SavedPlace> data = [];

        for (var i = 0; i < json['data'].length; i++) {
          data.add(
            SavedPlace.fromJson(
              json['data'][i],
            ),
          );
        }

        return data;
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<SavedPlace?> addPlace(SavedPlace place) async {
  Response response = await buildHttpResponse(
    'saved-places/add',
    method: HttpMethod.post,
    request: place.toJson(),
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return SavedPlace.fromJson(json['data']);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<bool?> deletePlace(SavedPlace place) async {
  Response response = await buildHttpResponse(
    'saved-places/delete/${place.id}',
    method: HttpMethod.post,
    request: place.toJson(),
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return json['status'];
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<VehicleTypeResponse?> getVehicleTypes() async {
  Response response = await buildHttpResponse(
      'service-list/?region_id=${Globals.riderRegionId}');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      try {
        var json = jsonDecode(response.body);
        return VehicleTypeResponse.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<VehicleDataResponse?> getVehicles() async {
  Response response = await buildHttpResponse('vehicles');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      try {
        var json = jsonDecode(response.body);

        return VehicleDataResponse.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<PaymentCardDataResponse?> getSavedCards() async {
  Response response = await buildHttpResponse('vehicles');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      try {
        var json = jsonDecode(response.body);

        return PaymentCardDataResponse.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<HomePageData?> getHomePageData() async {
  Response response = await buildHttpResponse(
    'get-rider-dashboard',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        if (json['response']['region_id'] != null &&
            json['response']['region_id'] == 0) {
          Globals.riderRegionId = json['response']['region_id'] ?? -1;
        }

        if (json['response']['region_code'] != null &&
            json['response']['region_code'].isNotEmpty) {
          Globals.riderRegionCode = json['response']['region_code'];
        }

        Globals.aboutUsText =
            json['response']['about_us_instruction_rider'] ?? '';
        Globals.deleteAccountText =
            json['response']['account_delete_instructions_for_rider'] ?? '';
        Globals.driverWaitingMinutes = json['response']
                ['max_time_for_find_drivers_for_regular_ride_in_minute'] ??
            5;

        /* counters */
        AppCounters.setInboxCount(json['response']['inbox_count'] ?? 0);
        AppCounters.setCareCount(json['response']['care_count'] ?? 0);
        AppCounters.setNotificationCount(
            json['response']['notification_count'] ?? 0);

        return HomePageData.fromJson(json['response']);
      } catch (error, stackTrace) {
        handleError(error, stackTrace);
        return null;
      }
    }

    return null;
  } else {
    return null;
  }
}

Future<Inbox?> getInboxData(
  int page,
  int riderId,
) async {
  Response response = await buildHttpResponse(
    'get-inbox/?page=$page&rider_id=$riderId',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return Inbox.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<bool?> deleteInboxMsg(
  int id,
) async {
  Response response = await buildHttpResponse(
    'delete-inbox-message/$id',
    method: HttpMethod.post,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return json['status'] as bool;
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<bool?> markReadInboxMsg(
  int id,
) async {
  Response response = await buildHttpResponse(
    'read-inbox-message/$id',
    method: HttpMethod.post,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return json['status'] as bool;
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<String?> getInboxMsgDetails(
  int id,
) async {
  Response response = await buildHttpResponse(
    'inbox-details/$id',
    // method: HttpMethod.POST,
  );

  // if ((response.statusCode >= 200 && response.statusCode <= 206)) {
  //   if (response.body.isJson()) {
  // var json = jsonDecode(response.body);
  // try {
  //   return json['status'] as bool;
  // } catch (e) {
  //   return null;
  // }
  // }
  // return null;
  return response.body;
  // } else {
  //   return null;
  // }
}

Future<FAQsResponse?> getFAQsData() async {
  Response response = await buildHttpResponse('faq-list');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      try {
        var json = jsonDecode(response.body);
        return FAQsResponse.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getFAQDetails(int id) async {
  Response response = await buildHttpResponse('faq-details/$id');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return json;
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<FAQsResponse?> getHelpData() async {
  Response response = await buildHttpResponse('help-list');

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      try {
        var json = jsonDecode(response.body);
        return FAQsResponse.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getHelpDetails(int id) async {
  Response response = await buildHttpResponse('help-details/$id');

  return response.body;
}

Future<dynamic> getBlogDetails(int id) async {
  Response response = await buildHttpResponse('rider-blog-details/$id');

  return response.body;
}

Future<Care?> getPendingCareData({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'care-pending-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return Care.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<Care?> getAdvertisements({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'care-pending-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return Care.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<Care?> getCompletedCareData({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'care-completed-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return Care.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<CareResponse?> getCareDetailsData({required int careId}) async {
  Response response = await buildHttpResponse(
    'care-detailed-list',
    request: {"care_id": careId},
    method: HttpMethod.post,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return CareResponse.fromMap(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<CareCommentResponse?> saveCareMessage(CareCommentRequest request) async {
  Response response = await buildHttpResponse('carecomment-store',
      method: HttpMethod.post, request: request.toMap());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return CareCommentResponse.fromMap(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<bool?> saveCareRequest(CareData request) async {
  Response response = await buildHttpResponse('care-save',
      method: HttpMethod.post, request: request.toJson());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      // var json = jsonDecode(response.body);
      try {
        return true;
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<SentOtpResponse> sendOTP(String mobile) async {
  Response response = await buildHttpResponse('send-otp',
      method: HttpMethod.post,
      request: {"contact_number": mobile, "user_type": "rider"});

  var json = jsonDecode(response.body);

  try {
    return SentOtpResponse.fromJson(json);
  } catch (e) {
    return SentOtpResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<SentOtpResponse> reSendOTP({required String verificationKey}) async {
  Response response =
      await buildHttpResponse('resend-otp', method: HttpMethod.post, request: {
    "contact_number": verificationKey,
  });

  var json = jsonDecode(response.body);

  try {
    return SentOtpResponse.fromJson(json);
  } catch (e) {
    return SentOtpResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<dynamic> verifyOTP(
  String key,
  String otp,
) async {
  Response response = await buildHttpResponse('validate-otp',
      method: HttpMethod.post,
      request: {
        'key': key,
        'otp': otp,
        'player_id': Globals.playerId,
        "user_type": "rider",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> verifyPassword(
  String key,
  String otp,
) async {
  Response response = await buildHttpResponse('validate-otp',
      method: HttpMethod.post,
      request: {
        'key': key,
        'password': otp,
        'player_id': Globals.playerId,
        "user_type": "rider",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> forgotPassword(
  String email,
) async {
  Response response = await buildHttpResponse('forget-password',
      method: HttpMethod.post,
      request: {
        'email': email,
        "user_type": "rider",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<ApiBaseResponse> saveWaitingTimeAndTipPreAuth({
  required String? holdPaymentId,
  required String? paymentCardId,
  required int rideRequestId,
  required int service_id,
  required num? tips,
  required num? waiting_charges,
  required num? due_amount,
  required num? refundable_amount,
  required num? advanced_paid,
  required num? pre_auth_amount,
  required num? walletAmount,
  num? rating,
  String? comment,
}) async {
  Map<String, dynamic> requestData = {
    'ride_request_id': rideRequestId,
    'hold_payment_id': holdPaymentId,
    'payment_card_id': paymentCardId,
    'service_id': service_id,
    'tips': tips,
    'waiting_charges': waiting_charges ?? 0,
    'due_amount': due_amount ?? 0,
    'refundable_amount': refundable_amount,
    'advanced_paid': advanced_paid,
    'pre_auth_amount': pre_auth_amount,
    'wallet_amount': walletAmount,
  };

  // Add rating and comment if provided
  if (rating != null) {
    requestData['rating'] = rating;
  }
  if (comment != null && comment.isNotEmpty) {
    requestData['comment'] = comment;
  }

  Response response = await buildHttpResponse('rider-complete-riderequest',
      method: HttpMethod.post, request: requestData);

  var json = jsonDecode(response.body);
  try {
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<dynamic> getCustomerDetailsForPayment({
  required int riderId,
}) async {
  Response response = await buildHttpResponse('rider-complete-riderequest',
      method: HttpMethod.post,
      request: {
        'id': riderId,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getSuggestedReviewMsgs() async {
  Response response = await buildHttpResponse(
    'get-review-auto-suggestive-message',
    method: HttpMethod.get,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> sendEmailVerificationCode({
  required String email,
}) async {
  Response response =
      await buildHttpResponse('hello', method: HttpMethod.post, request: {
    'email': email,
  });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> changeRiderEmail(
    {required String email, required String verificationCode}) async {
  Response response = await buildHttpResponse('update-email',
      method: HttpMethod.post,
      request: {
        'email2': email,
        // 'code': verificationCode,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> sendMobileVerificationCode({
  required String mobile,
}) async {
  Response response = await buildHttpResponse('update-contact-number',
      method: HttpMethod.post,
      request: {
        'contact_number2': mobile,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> changeRiderMobile(
    {required String mobile, required String verificationCode}) async {
  Response response = await buildHttpResponse('validate-otp-contact2',
      method: HttpMethod.post,
      request: {
        'mobile': mobile,
        'otp': verificationCode,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> verifyEmail(
    // required String email,
    ) async {
  Response response = await buildHttpResponse('sent-verification-email',
      method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getPickupScreenData(
    // required String email,
    ) async {
  Response response =
      await buildHttpResponse('get-pickup-lines', method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getRegionsData(
    // required String email,
    ) async {
  Response response =
      await buildHttpResponse('region-list', method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return jsonDecode(response.body);
    }
    return null;
  } else {
    return null;
  }
}

Future<ProvinceModelResponse?> getProvincesData() async {
  Response response =
      await buildHttpResponse('province-list', method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      return ProvinceModelResponse.fromJson(jsonDecode(response.body));
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> checkAppleSignup(
  String code,
) async {
  Response response = await buildHttpResponse('get-apple-data',
      request: {
        'apple_id': code,
      },
      method: HttpMethod.post);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

Future<ApiBaseResponse> saveAppleSignupData({
  required String appleId,
  required String appleEmail,
  required String firstName,
  required String lastName,
}) async {
  Response response = await buildHttpResponse('save-apple-data',
      request: {
        'apple_id': appleId,
        'email': appleEmail,
        'first_name': firstName,
        'last_name': lastName,
      },
      method: HttpMethod.post);

  try {
    var json = jsonDecode(response.body);
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<dynamic> getTokenToSaveCard(
  String customerId,
) async {
  Response response =
      await buildHttpResponse('get-setup-intent', method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

Future<dynamic> getInvoiceLink(
  int rideId,
) async {
  Response response = await buildHttpResponse('ride-request-invoice/$rideId',
      method: HttpMethod.get);

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);

      return json;
    }
    return null;
  } else {
    return null;
  }
}

Future<SelectServiceResponseModel> getServicesList(
    {required Map request}) async {
  Response response = await buildHttpResponse(
    'get-driver-detail',
    request: request,
    method: HttpMethod.post,
  );
  var json = jsonDecode(response.body);
  try {
    return SelectServiceResponseModel.fromJson(json);
  } catch (e) {
    return SelectServiceResponseModel(
        status: false, message: Globals.language.errorMsg, data: []);
  }
}

Future<AddRideStopResponse> addRideStop({required RideStop request}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'multi-stop-ride',
        request: request.toJson(),
        method: HttpMethod.post,
      ),
    );
    if (json["status"] == false &&
        json["data"] != null &&
        json["data"]["is_flag"] == true) {
      return AddRideStopResponse(
        status: false,
        message: json["message"],
        data: RideStop(
          latitude: 0,
          longitude: 0,
          currentLatitude: 0,
          currentLongitude: 0,
          currentAddress: "",
          title: "",
          rideId: null,
          id: 0,
          isThePriceIncreasedAfterThisStop: true,
          payablePrice: json["data"]["payable_amount"],
          debitWallet: null,
          paymentCardId: null,
          isArrived: null,
        ),
      );
    }
    return AddRideStopResponse.fromJson(json);
  } catch (e) {
    return AddRideStopResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> deleteRideStop({
  required int stopId,
  required int rideId,
}) async {
  try {
    return ApiBaseResponse.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'delete-stop',
          request: {
            "ride_request_id": rideId,
            "id": stopId,
          },
          method: HttpMethod.post,
        ),
      ),
    );
  } catch (e) {
    return StopEstimatePriceResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse<RideDestination>> addRideDestination(
    {required RideDestination request}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'ride-destination-update',
        request: request.toJson(),
        method: HttpMethod.post,
      ),
    );
    if (json["status"] == false &&
        json["data"] != null &&
        json["data"]["is_flag"] == true) {
      return ApiBaseResponse(
        status: false,
        message: json["message"],
        data: RideDestination(
          latitude: 0,
          longitude: 0,
          currentLatitude: 0,
          currentLongitude: 0,
          currentAddress: "",
          title: "",
          rideId: null,
          id: 0,
          isThePriceIncreasedAfterThisStop: true,
          payablePrice: json["data"]["payable_amount"],
        ),
      );
    }
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> changeRidePickupLocation(
    {required RidePickup request}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'change-pickup',
        request: {
          "id": request.rideId,
          "lat": request.latitude,
          "lng": request.longitude,
          "current_lat": request.currentLatitude,
          "current_lng": request.currentLongitude,
          "current_address": request.currentAddress,
          "title": request.title,
        },
        method: HttpMethod.post,
      ),
    );
    if (json["status"] == false &&
        json["data"] != null &&
        json["data"]["is_flag"] == true) {
      return ApiBaseResponse(
        status: false,
        message: json["message"],
        data: RidePickup(
          latitude: 0,
          longitude: 0,
          currentLatitude: 0,
          currentLongitude: 0,
          currentAddress: "",
          title: "",
          rideId: null,
          id: 0,
          // isThePriceIncreasedAfterThisStop: true,
          // payablePrice: json["data"]["payable_amount"],
        ),
      );
    }
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    String message = e.toString();
    if (e.runtimeType != String) {
      message = Globals.language.errorMsg;
    }
    return ApiBaseResponse(
      status: false,
      message: message,
      data: null,
    );
  }
}

Future<ApiBaseResponse> savePaymentDetailsForStop({
  required int rideId,
  required num? debitFromWallet,
  required String? paymentCardId,
  required String? holdPaymentId,
  required num totalAmountToPay,
}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'update-stop',
        request: {
          "ride_id": rideId,
          "debitwallet": debitFromWallet,
          "payment_card_id": paymentCardId,
          "hold_payment_id": holdPaymentId,
          "pending_amount": totalAmountToPay,
          "type": "stop",
        },
        method: HttpMethod.post,
      ),
    );
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> savePaymentDetailsForDeatination({
  required int rideId,
  required num? debitFromWallet,
  required String? paymentCardId,
  required String? holdPaymentId,
  required num totalAmountToPay,
}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'update-stop',
        request: {
          "ride_id": rideId,
          "debitwallet": debitFromWallet,
          "payment_card_id": paymentCardId,
          "hold_payment_id": holdPaymentId,
          "pending_amount": totalAmountToPay,
          "type": "destination",
        },
        method: HttpMethod.post,
      ),
    );
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> savePaymentDetailsForPickup({
  required int rideId,
  required num? debitFromWallet,
  required String? paymentCardId,
  required String? holdPaymentId,
  required num totalAmountToPay,
}) async {
  try {
    var json = await handleResponse(
      await buildHttpResponse(
        'update-stop',
        request: {
          "ride_id": rideId,
          "debitwallet": debitFromWallet,
          "payment_card_id": paymentCardId,
          "hold_payment_id": holdPaymentId,
          "pending_amount": totalAmountToPay,
          "type": "pickup",
        },
        method: HttpMethod.post,
      ),
    );
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> addWalletAmountWithStripeCard({
  required String paymentCardId,
  required int walletAmount,
}) async {
  try {
    return ApiBaseResponse.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'amount-add-through-card',
          request: {
            "payment_card_id": paymentCardId,
            "amount": walletAmount,
          },
          method: HttpMethod.post,
        ),
      ),
    );
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<ApiBaseResponse> getAgoraCallingDetails() async {
  try {
    return ApiBaseResponse.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'amount-add-through-card',
          method: HttpMethod.get,
        ),
      ),
    );
  } catch (e) {
    return ApiBaseResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<StatusMessageModel> updatePlayerId(
    {required String playerId, required String timezone}) async {
  Response response = await buildHttpResponse('update-user-playerid',
      method: HttpMethod.post,
      request: {"player_id": playerId, "timezone": timezone});

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return StatusMessageModel.fromJson(json);
      } catch (e) {
        return StatusMessageModel(status: false, message: "");
      }
    }
    return StatusMessageModel(status: false, message: "");
  } else {
    return StatusMessageModel(status: false, message: "");
  }
}

Future<AdsServiceResponse> getAdData({
  required String adType,
}) async {
  Response response = await buildHttpResponse(
    'advertisements?type=$adType',
    method: HttpMethod.get,
  );

  var json = jsonDecode(response.body);
  try {
    return AdsServiceResponse.fromJson(json);
  } catch (e) {
    return AdsServiceResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<WalletResponseModel> getWalletDetails({
  required int userId,
}) async {
  Response response = await buildHttpResponse(
    'wallet-detail',
    method: HttpMethod.get,
    request: {
      "user_id": userId.toString(),
    },
  );

  var json = jsonDecode(response.body);
  try {
    return WalletResponseModel.fromJson(json);
  } catch (e) {
    return WalletResponseModel(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<RegisterResponse> updateUser({
  required Map<String, dynamic> user,
}) async {
  Response response = await buildHttpResponse(
    'update-user',
    method: HttpMethod.post,
    request: user,
  );

  var json = jsonDecode(response.body);
  try {
    return RegisterResponse.fromJson(json);
  } catch (e) {
    return RegisterResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<RideRelatedSettingsResponse> updateRideSettings({
  required RideRelatedSettings request,
}) async {
  Response response = await buildHttpResponse(
    'update-ride-settings',
    method: HttpMethod.post,
    request: request.toMap(),
  );

  var json = jsonDecode(response.body);
  try {
    return RideRelatedSettingsResponse.fromMap(json);
  } catch (e) {
    return RideRelatedSettingsResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<StatusMessageModel> updateUserSettings({
  required Map<String, dynamic> request,
}) async {
  Response response = await buildHttpResponse(
    'update-user-status',
    method: HttpMethod.post,
    request: request,
  );

  var json = jsonDecode(response.body);
  try {
    return StatusMessageModel.fromJson(json);
  } catch (e) {
    return StatusMessageModel(
      status: false,
      message: Globals.language.errorMsg,
    );
  }
}

Future<RideRelatedSettingsResponse> getRideSettings() async {
  Response response = await buildHttpResponse(
    'get-ride-settings',
    method: HttpMethod.get,
  );

  var json = jsonDecode(response.body);
  try {
    return RideRelatedSettingsResponse.fromMap(json);
  } catch (e) {
    return RideRelatedSettingsResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<CouponModel> getCouponListApi() async {
  Response response = await buildHttpResponse(
    'coupon-list',
    method: HttpMethod.get,
  );

  var json = jsonDecode(response.body);
  try {
    return CouponModel.fromJson(json);
  } catch (e) {
    return CouponModel(
        status: false,
        message: Globals.language.errorMsg,
        data: [],
        pagination: null);
  }
}

Future<PushNotificationTypesResponse> getPushNotificationSettings() async {
  Response response = await buildHttpResponse(
    'notification-type',
    method: HttpMethod.get,
  );

  var json = jsonDecode(response.body);
  try {
    return PushNotificationTypesResponse.fromMap(json);
  } catch (e) {
    return PushNotificationTypesResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<ApiBaseResponse> savePushNotificationSettings(
    {required List<int> typeIds}) async {
  String typeIdsString = typeIds.isEmpty ? "" : typeIds.toString();
  if (typeIdsString.isNotEmpty) {
    typeIdsString = typeIdsString.substring(1, typeIdsString.length - 1);
  }

  Response response = await buildHttpResponse(
    'update-user-status',
    method: HttpMethod.post,
    request: {
      "notif_type": typeIdsString,
    },
  );

  var json = jsonDecode(response.body);
  try {
    return ApiBaseResponse.fromJson(json);
  } catch (e) {
    return ApiBaseResponse(
        status: false, message: Globals.language.errorMsg, data: null);
  }
}

Future<ApiBaseResponse<Null>> sendOTPToEmail({required String email}) async {
  Response response = await buildHttpResponse('send-otp-email',
      method: HttpMethod.post,
      request: {
        'email': email,
        "user_type": "rider",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var data = jsonDecode(response.body);
      return ApiBaseResponse(
        status: data["status"],
        message: data["message"],
        data: null,
      );
    }
    return ApiBaseResponse(status: false, message: "Server error", data: null);
  } else {
    return ApiBaseResponse(status: false, message: "Server error", data: null);
  }
}

Future<ApiBaseResponse<Null>> verifyEmailOTP(
    {required String email, required String otp}) async {
  Response response = await buildHttpResponse('verify-otp-email',
      method: HttpMethod.post,
      request: {
        'email': email,
        'otp': otp,
        'user_type': "rider",
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var data = jsonDecode(response.body);
      return ApiBaseResponse(
        status: data["status"],
        message: data["message"],
        data: null,
      );
    }
    return ApiBaseResponse(status: false, message: "Server error", data: null);
  } else {
    return ApiBaseResponse(status: false, message: "Server error", data: null);
  }
}

Future<StopEstimatePriceResponse> getRideStopEstimatePrice(
    {required int rideId}) async {
  try {
    return StopEstimatePriceResponse.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'get-stop',
          request: {
            "ride_request_id": rideId,
            "type": "stop",
          },
          method: HttpMethod.post,
        ),
      ),
    );
  } catch (e) {
    return StopEstimatePriceResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<StopEstimatePriceResponse> getRideChangeDestinationPrice(
    {required int rideId}) async {
  try {
    return StopEstimatePriceResponse.fromJson(
      await handleResponse(
        await buildHttpResponse(
          'get-stop',
          request: {
            "ride_request_id": rideId,
            "type": "destination",
          },
          method: HttpMethod.post,
        ),
      ),
    );
  } catch (e) {
    return StopEstimatePriceResponse(
      status: false,
      message: Globals.language.errorMsg,
      data: null,
    );
  }
}

Future<LastCompletedRidesResponse?> getLastCompletedRideLocations() async {
  try {
    Response response = await buildHttpResponse('get-last-destinations',
        method: HttpMethod.get);
    var json = jsonDecode(response.body);
    return LastCompletedRidesResponse.fromMap(json);
  } catch (e) {
    return null;
  }
}

Future<RideHelp?> getPendingHelps({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'help-pending-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelp.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<RideHelp?> getCompletedHelps({
  int page = 1,
}) async {
  Response response = await buildHttpResponse(
    'help-complete-list/?page=$page',
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelp.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<RideHelpDetails?> getRideHelpDetailsData({
  required int helpId,
  required int page,
}) async {
  Response response = await buildHttpResponse(
    'complaintcomment-list/?complaint_id=$helpId&page=$page',
    method: HttpMethod.get,
  );

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return RideHelpDetails.fromJson(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<HelpCommentResponse?> saveRideHelpMessage(
    HelpCommentRequest request) async {
  Response response = await buildHttpResponse('helpcomment-store',
      method: HttpMethod.post, request: request.toMap());

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return HelpCommentResponse.fromMap(json);
      } catch (e) {
        return null;
      }
    }
    return null;
  } else {
    return null;
  }
}

Future<DriverEstimatedTimeResponse> getDriverEstimatedTime({
  required int rideId,
}) async {
  Response response = await buildHttpResponse('driver-arrival-time',
      method: HttpMethod.post,
      request: {
        "ride_id": rideId,
      });

  if ((response.statusCode >= 200 && response.statusCode <= 206)) {
    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      try {
        return DriverEstimatedTimeResponse.fromMap(json);
      } catch (e) {
        return DriverEstimatedTimeResponse(
            message: "", status: false, data: null);
      }
    }
    return DriverEstimatedTimeResponse(message: "", status: false, data: null);
  } else {
    return DriverEstimatedTimeResponse(message: "", status: false, data: null);
  }
}

Future<CounterDataResponse> getCounterData() async {
  try {
    final response =
        await buildHttpResponse('notification-counter', method: HttpMethod.get);
    final jsonData = await handleResponse(response);
    return CounterDataResponse.fromMap(jsonData);
  } catch (e) {
    return CounterDataResponse(
        status: false, message: "Error fetching counter data", data: null);
  }
}

Future<AccountDeletionResponse> checkAccountDeletionStatus() async {
  try {
    Response response = await buildHttpResponse(
      'check-account-deletion-status',
      method: HttpMethod.post,
    );

    if (response.body.isJson()) {
      var json = jsonDecode(response.body);
      return AccountDeletionResponse.fromMap(json);
    }
    return AccountDeletionResponse(
        status: false, message: "Server error", data: null);
  } catch (e) {
    return AccountDeletionResponse(
        status: false, message: "Server error", data: null);
  }
}

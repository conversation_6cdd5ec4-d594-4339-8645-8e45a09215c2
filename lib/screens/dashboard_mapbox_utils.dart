import 'package:flutter/material.dart';
import 'package:mapbox_maps_flutter/mapbox_maps_flutter.dart' as mp;
import 'package:location/location.dart' as nlp;
import 'package:rider/globals.dart';
import 'package:rider/services/map_box_service.dart';
import 'package:rider/services/models/annotation_managers.dart';

class DashboardMapBoxUtils {
  mp.MapboxMap? _mapboxMap;
  AnnotationManagers? _annotationManagers;
  mp.PointAnnotation? _currentLocationPoint;
  nlp.LocationData? _lastKnownLocation;
  Function(nlp.LocationData)? onLocationChanged;
  nlp.LocationData? get lastKnownLocation => _lastKnownLocation;
  bool _isLocationTrackingStarted = false;
  bool _isAlreadyFlying = false;
  bool get isLocationTrackingActive => _isLocationTrackingStarted;

  Future<void> initializeMap(mp.MapboxMap mapboxMap) async {
    _mapboxMap = mapboxMap;

    _mapboxMap!.scaleBar.updateSettings(mp.ScaleBarSettings(enabled: false));
    _annotationManagers =
        await MapBoxService.initializeAnnotationManagers(mapboxMap);

    // If we already have a location, fly to it immediately
    if (Globals.currentLocation != null) {
      _lastKnownLocation = nlp.LocationData.fromMap({
        "latitude": Globals.currentLocation!.lat,
        "longitude": Globals.currentLocation!.lng,
      });
      // Add a small delay to ensure map is fully loaded
      await Future.delayed(const Duration(milliseconds: 500));
      await flyToCurrentLocation();
    }
  }

  Future<void> startLocationTracking() async {
    if (_mapboxMap == null) {
      throw ('Cannot start location tracking: Dashboard MapBox not initialized');
    }

    if (_isLocationTrackingStarted) {
      return;
    }

    _isLocationTrackingStarted = true;

    nlp.Location location = nlp.Location();
    nlp.LocationData currentLocation = await location.getLocation();
    _lastKnownLocation = currentLocation;
    Globals.currentLocation = mp.Position(currentLocation.longitude!.toDouble(),
        currentLocation.latitude!.toDouble());

    _currentLocationPoint ??= await MapBoxService.createMarkerFromAsset(
      manager: _annotationManagers!.pointManager,
      position: mp.Position(
        currentLocation.longitude!.toDouble(),
        currentLocation.latitude!.toDouble(),
      ),
      assetPath: 'assets/images/riderIcon.png',
      iconSize: 0.3,
    );

    // Immediately fly to current location when tracking starts
    await flyToCurrentLocation();

    location.onLocationChanged.listen((locationData) async {
      if (_mapboxMap == null ||
          _currentLocationPoint == null ||
          _annotationManagers == null) {
        return;
      }

      if (locationData.latitude != null) {
        _lastKnownLocation = locationData;
        Globals.currentLocation = mp.Position(
          locationData.longitude!.toDouble(),
          locationData.latitude!.toDouble(),
        );

        if (_currentLocationPoint != null) {
          _currentLocationPoint!.geometry = mp.Point(
            coordinates: mp.Position(
              locationData.longitude!.toDouble(),
              locationData.latitude!.toDouble(),
            ),
          );

          await MapBoxService.updateAnnotation(
            manager: _annotationManagers?.pointManager,
            annotation: _currentLocationPoint,
          );
        }
        if (onLocationChanged != null) {
          onLocationChanged!(locationData);
        }
      }
    });
  }

  Future<void> flyToCurrentLocation({double zoom = 16.0}) async {
    if (_mapboxMap == null || _lastKnownLocation == null) {
      return;
    }

    // Only prevent flying if we're already flying and it's been less than 1 second
    if (_isAlreadyFlying == true) {
      return;
    }

    _isAlreadyFlying = true;
    await MapBoxService.flyToPosition(
      mapboxMap: _mapboxMap!,
      position: mp.Position(
        _lastKnownLocation!.longitude!.toDouble(),
        _lastKnownLocation!.latitude!.toDouble(),
      ),
      zoom: zoom,
    );
    // Reduce delay to make recentering more responsive
    await Future.delayed(const Duration(milliseconds: 500));

    _isAlreadyFlying = false;
  }

  Future<void> dispose() async {
    if (_annotationManagers != null) {
      await MapBoxService.clearAnnotations(_annotationManagers!.pointManager);
      await MapBoxService.clearAnnotations(
          _annotationManagers!.polylineManager);
      await MapBoxService.clearAnnotations(_annotationManagers!.circleManager);
    }

    // Reset all variables
    _mapboxMap = null;
    _annotationManagers = null;
    _currentLocationPoint = null;
    _lastKnownLocation = null;
    _isLocationTrackingStarted = false;
    _isAlreadyFlying = false;
  }

  void changeMapTheme(ThemeData theme) {
    if (_mapboxMap != null) {
      _mapboxMap!.loadStyleURI(
        theme.brightness != Brightness.dark
            ? mp.MapboxStyles.DARK
            : mp.MapboxStyles.STANDARD,
      );
    }
  }
}

/* MapBox widget for dashboard */
class DashboardMapWidget extends StatefulWidget {
  final Function(mp.MapboxMap)? onMapCreated;
  final Function(DashboardMapBoxUtils)? onMapBoxUtilsCreated;

  const DashboardMapWidget({
    super.key,
    this.onMapCreated,
    this.onMapBoxUtilsCreated,
  });

  @override
  State<DashboardMapWidget> createState() => _DashboardMapWidgetState();
}

class _DashboardMapWidgetState extends State<DashboardMapWidget> {
  final DashboardMapBoxUtils _mapBoxUtils = DashboardMapBoxUtils();

  @override
  void dispose() {
    _mapBoxUtils.dispose();
    super.dispose();
  }

  void _onMapCreated(mp.MapboxMap mapboxMap) async {
    await _mapBoxUtils.initializeMap(mapboxMap);

    if (widget.onMapBoxUtilsCreated != null) {
      widget.onMapBoxUtilsCreated!(_mapBoxUtils);
    }

    if (widget.onMapCreated != null) {
      widget.onMapCreated!(mapboxMap);
    }
  }

  @override
  Widget build(BuildContext context) {
    // Use a more reasonable default position (Australia center) with better zoom
    return mp.MapWidget(
      styleUri: Theme.of(context).brightness == Brightness.dark
          ? mp.MapboxStyles.DARK
          : mp.MapboxStyles.STANDARD,
      cameraOptions: mp.CameraOptions(
        center: mp.Point(coordinates: mp.Position(133.2096, -25.9437)),
        zoom: 4, // Start with a wider view to show more context
      ),
      onMapCreated: _onMapCreated,
    );
  }
}

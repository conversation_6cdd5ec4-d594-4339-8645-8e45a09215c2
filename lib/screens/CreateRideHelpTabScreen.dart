import 'package:rider/app_exports.dart';
import 'package:rider/model/PaginationModel.dart';
import 'package:rider/screens/RideHelpdetailsScreen.dart';

import '../utils/Extensions/app_common.dart';

class CreateRideHelpTabScreen extends StatefulWidget {
  final String? status;
  final Function() indicatorUpdater;

  CreateRideHelpTabScreen({
    this.status,
    required this.indicatorUpdater,
  });

  @override
  CreateCareTabScreenState createState() => CreateCareTabScreenState();
}

class CreateCareTabScreenState extends State<CreateRideHelpTabScreen>
    with AutomaticKeepAliveClientMixin {
  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;
  List<RideHelpData> helpData = [];
  String emptyDataMsg = '';

  @override
  void initState() {
    showAppActivity();
    super.initState();
    init();
    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          showAppActivity();
          currentPage++;
          setState(() {});

          init();
        }
      }
    });
    afterBuildCreated(() => showAppActivity());
  }

  Future<void> init() async {
    late Future<RideHelp?> task;
    if (widget.status == "pending") {
      task = getPendingHelps(
        page: currentPage,
      );
    } else {
      task = getCompletedHelps(
        page: currentPage,
      );
    }
    await task.then((value) {
      if (value != null) {
        if (widget.status == "pending") {
          widget.indicatorUpdater();
        }

        hideAppActivity();
        currentPage = value.pagination.currentPage!;
        totalPage = value.pagination.totalPages!;
        emptyDataMsg = value.message ?? '';
        if (currentPage == 1) {
          helpData.clear();
        }
        setState(() {
          helpData.addAll(value.data);
        });
      } else {
        showErrorToast();
      }
    }).onError((error, stackTrace) {
      hideAppActivity();
      log(error.toString());
      handleError(error, stackTrace);
    });
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    return Stack(
      children: [
        AnimationLimiter(
          child: RefreshIndicator(
            onRefresh: () {
              currentPage = 1;
              setState(() {
                hideAppActivity();
              });
              return init();
            },
            child: ListView.builder(
                physics: const AlwaysScrollableScrollPhysics(),
                itemCount: helpData.length,
                controller: scrollController,
                padding: const EdgeInsets.only(
                    top: 8, bottom: 8, left: 16, right: 16),
                itemBuilder: (_, index) {
                  RideHelpData data = helpData[index];
                  return AnimationConfiguration.staggeredList(
                    delay: const Duration(milliseconds: 200),
                    position: index,
                    duration: const Duration(milliseconds: 375),
                    child: SlideAnimation(
                      child: IntrinsicHeight(
                        child: inkWellWidget(
                          onTap: () {
                            launchScreen(
                              RideHelpDetailsScreen(
                                helpData: data,
                                isClosed: widget.status == "closed",
                                cardStateUpdater: () {
                                  setState(() {
                                    data.help_count = 0;
                                  });
                                },
                              ),
                            );
                          },
                          child: Container(
                            padding: const EdgeInsets.only(top: 8, bottom: 8),
                            margin: const EdgeInsets.only(top: 8, bottom: 8),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.4),
                                  blurRadius: 10,
                                  spreadRadius: 0,
                                  offset: const Offset(0.0, 0.0),
                                ),
                              ],
                            ),
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          data.subject,
                                          style: const TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18,
                                            color: Colors.black,
                                          ),
                                        ),
                                      ),
                                      Visibility(
                                        visible: data.help_count != 0,
                                        child: Container(
                                          height: 20,
                                          width: 20,
                                          decoration: const BoxDecoration(
                                              color: Colors.red,
                                              shape: BoxShape.circle),
                                          child: Center(
                                            child: Text(
                                              data.help_count.toString(),
                                              style: const TextStyle(
                                                color: Colors.black,
                                                fontSize: 8,
                                              ),
                                            ),
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  const Divider(height: 24, thickness: 0.5),
                                  Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Row(
                                        children: [
                                          const Icon(
                                            Ionicons.calendar,
                                            size: 16,
                                            color: Colors.black,
                                          ),
                                          const SizedBox(width: 8),
                                          Padding(
                                            padding:
                                                const EdgeInsets.only(top: 2),
                                            child: Text(
                                                '${printDate(data.booking_date)}',
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14)),
                                          ),
                                        ],
                                      ),
                                      Text(
                                        'Ride #${data.ride_request_id}',
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const Divider(height: 24, thickness: 0.5),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Column(
                                        children: [
                                          Icon(Icons.near_me,
                                              color: Colors.green, size: 18),
                                          SizedBox(height: 2),
                                          SizedBox(
                                            height: 34,
                                            child: DottedLine(
                                              direction: Axis.vertical,
                                              lineLength: double.infinity,
                                              lineThickness: 1,
                                              dashLength: 2,
                                              dashColor: Colors.black,
                                            ),
                                          ),
                                          SizedBox(height: 2),
                                          Icon(Icons.location_on,
                                              color: Colors.red, size: 18),
                                        ],
                                      ),
                                      const SizedBox(width: 16),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          mainAxisAlignment:
                                              MainAxisAlignment.start,
                                          children: [
                                            SizedBox(
                                              height: 38,
                                              child: Text(
                                                data.booking_from,
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                            const SizedBox(height: 17),
                                            SizedBox(
                                              height: 38,
                                              child: Text(
                                                data.booking_to,
                                                style: const TextStyle(
                                                    color: Colors.black,
                                                    fontSize: 14),
                                                maxLines: 2,
                                                overflow: TextOverflow.ellipsis,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                  );
                }),
          ),
        ),
        Visibility(
          visible: isAppActivityRunning.value,
          child: loaderWidget(),
        ),
        if (helpData.isEmpty && !isAppActivityRunning.value)
          emptyWidget(emptyDataMsg: emptyDataMsg),
      ],
    );
  }

  @override
  bool get wantKeepAlive => true;
}

class RideHelp {
  PaginationModel pagination;
  List<RideHelpData> data;
  String? message;
  RideHelp({required this.pagination, required this.data, this.message});

  factory RideHelp.fromJson(Map<String, dynamic> json) {
    List<RideHelpData> data = [];
    for (var i = 0; i < json['data'].length; i++) {
      data.add(
        RideHelpData.fromJson(
          json['data'][i],
        ),
      );
    }
    return RideHelp(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class RideHelpData {
  int id;
  int ride_request_id;
  int help_count;
  String subject;
  String description;
  String booking_date;
  String booking_from;
  String booking_to;
  String created_at;
  String? image;
  RideHelpData({
    required this.id,
    required this.booking_date,
    required this.booking_from,
    required this.booking_to,
    required this.created_at,
    required this.help_count,
    required this.ride_request_id,
    required this.subject,
    required this.description,
    this.image,
  });

  factory RideHelpData.fromJson(Map<String, dynamic> json) {
    return RideHelpData(
      id: json['id'] as int,
      booking_date: json['booking_date'] as String,
      booking_from: json['booking_from'] as String,
      booking_to: json['booking_to'] as String,
      created_at: json['created_at'] as String,
      help_count: json['help_count'] as int,
      ride_request_id: json['ride_request_id'] as int,
      subject: json['subject'] as String,
      description: json['description'] as String,
      image: json['image'] as String?,
    );
  }
}

class RideHelpComment {
  final int id;
  final int careId;
  final int userId;
  final String addedBy;
  final String? status;
  final String comment;
  final DateTime createdAt;
  final DateTime? updatedAt;

  RideHelpComment({
    required this.id,
    required this.careId,
    required this.userId,
    required this.addedBy,
    required this.status,
    required this.comment,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RideHelpComment.fromMap(Map<String, dynamic> map) {
    return RideHelpComment(
      id: map['id'] as int,
      careId: map['complaint_id'] as int,
      userId: map['user_id'] as int,
      addedBy: map['added_by'] as String,
      status: map['status'] as String?,
      comment: map['comment'] as String,
      createdAt: DateTime.parse(map['created_at'] as String),
      updatedAt: map['updated_at'] == null
          ? null
          : DateTime.parse(map['updated_at'] as String),
    );
  }
}

class RideHelpDetails {
  PaginationModel pagination;
  List<RideHelpComment> data;
  String? message;
  RideHelpDetails({required this.pagination, required this.data, this.message});

  factory RideHelpDetails.fromJson(Map<String, dynamic> json) {
    List<RideHelpComment> data = [];
    for (var i = 0; i < json['data']['helpComment'].length; i++) {
      data.add(
        RideHelpComment.fromMap(
          json['data']['helpComment'][i],
        ),
      );
    }
    return RideHelpDetails(
        pagination: PaginationModel.fromJson(
          json['pagination'],
        ),
        data: data,
        message: json['message']);
  }
}

class HelpCommentRequest {
  final int userId;
  final String addedBy;
  final String complaintId;
  final String comment;

  HelpCommentRequest({
    required this.userId,
    required this.addedBy,
    required this.complaintId,
    required this.comment,
  });

  Map<String, dynamic> toMap() {
    return {
      'user_id': userId,
      'added_by': addedBy,
      'complaint_id': complaintId,
      'comment': comment,
    };
  }
}

class HelpCommentResponse {
  final bool status;
  final String message;
  final RideHelpComment? data;

  HelpCommentResponse({
    required this.status,
    required this.message,
    required this.data,
  });

  factory HelpCommentResponse.fromMap(Map<String, dynamic> map) {
    return HelpCommentResponse(
      status: map["status"] as bool,
      message: map["message"] as String,
      data: map["data"] == null ? null : RideHelpComment.fromMap(map["data"]),
    );
  }
}

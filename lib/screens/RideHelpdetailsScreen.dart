import 'package:intl/intl.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:rider/app_exports.dart';
import 'package:rider/screens/CreateRideHelpTabScreen.dart';

class RideHelpDetailsScreen extends StatefulWidget {
  final RideHelpData helpData;
  final bool isClosed;
  final Function() cardStateUpdater;

  const RideHelpDetailsScreen({
    super.key,
    required this.helpData,
    required this.cardStateUpdater,
    this.isClosed = false,
  });

  @override
  State<RideHelpDetailsScreen> createState() => _CareDetailsScreenState();
}

class _CareDetailsScreenState extends State<RideHelpDetailsScreen> {
  List<RideHelpComment> _messages = [];

  TextEditingController _messageCont = TextEditingController();

  FocusNode _focusNode = FocusNode();

  late int userId;

  ScrollController scrollController = ScrollController();

  int currentPage = 1;
  int totalPage = 1;

  String title = "fetching details...";

  @override
  void initState() {
    showAppActivity();
    userId = Globals.user.id;

    scrollController.addListener(() {
      if (scrollController.position.pixels ==
          scrollController.position.maxScrollExtent) {
        if (currentPage < totalPage) {
          currentPage++;

          _loadData();

          setState(() {
            showAppActivity();
          });
        }
      }
    });

    _loadData();

    super.initState();
  }

  @override
  void dispose() {
    hideAppActivity();
    super.dispose();
  }

  Future<void> _loadData() async {
    var result = await getRideHelpDetailsData(
        helpId: widget.helpData.id, page: currentPage);
    if (result == null) {
      toast("Something went wrong");
    } else {
      try {
        totalPage = result.pagination.totalPages ?? 1;

        if (currentPage == 1) {
          _messages.clear();
        }
        _messages.addAll(result.data);

        widget.cardStateUpdater();
        setState(() {
          title = widget.helpData.subject;
          hideAppActivity();
        });
      } catch (e) {
        print("object");
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: Scaffold(
        appBar: RoooAppbar(title: title),
        body: Stack(
          children: [
            Padding(
              padding: EdgeInsets.fromLTRB(
                10,
                0,
                10,
                widget.isClosed
                    ? 0
                    : Platform.isIOS
                        ? 90
                        : 70,
              ),
              child: Column(
                children: [
                  if (widget.helpData.image != null &&
                      widget.helpData.image!.isNotEmpty)
                    Card(
                      child: ListTile(
                        leading: const Icon(Icons.attach_file),
                        title: const Text('Attachment'),
                        // subtitle: Text(
                        //   widget.helpData.image!.substring(
                        //       widget.helpData.image!.lastIndexOf('/') + 1),
                        // ),
                        trailing: IconButton(
                          icon: const Icon(Icons.download),
                          onPressed: () async {
                            if (await canLaunchUrl(
                                Uri.parse(widget.helpData.image!))) {
                              await launchUrl(
                                  Uri.parse(widget.helpData.image!));
                            } else {
                              toast('Could not open attachment');
                            }
                          },
                        ),
                      ),
                    ),
                  Expanded(
                    child: ListView.builder(
                      controller: scrollController,
                      reverse: true,
                      itemBuilder: (context, index) {
                        return RideHelpChatWidget(data: _messages[index]);
                      },
                      itemCount: _messages.length,
                    ),
                  ),
                ],
              ),
            ),
            widget.isClosed
                ? const SizedBox()
                : Positioned(
                    bottom: Platform.isIOS ? 28 : 7,
                    left: 0,
                    right: 0,
                    child: Padding(
                      padding: const EdgeInsets.all(10.0),
                      child: Container(
                        decoration: BoxDecoration(
                          borderRadius: radius(),
                          color: Theme.of(context).cardColor,
                          boxShadow: [
                            const BoxShadow(
                              spreadRadius: 0.5,
                              blurRadius: 0.5,
                            ),
                          ],
                        ),
                        padding: const EdgeInsets.only(left: 8, right: 8),
                        child: Row(
                          children: [
                            Expanded(
                              child: TextField(
                                focusNode: _focusNode,
                                controller: _messageCont,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: "Message",
                                  hintStyle: secondaryTextStyle(),
                                  contentPadding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                ),
                                textCapitalization:
                                    TextCapitalization.sentences,
                                keyboardType: TextInputType.multiline,
                                minLines: 1,
                                style: primaryTextStyle(),
                                textInputAction: TextInputAction.newline,
                                onSubmitted: (s) {
                                  sendMessage();
                                },
                                cursorHeight: 20,
                                maxLines: 5,
                              ),
                            ),
                            IconButton(
                              icon: const Icon(
                                Icons.send,
                              ),
                              onPressed: () {
                                sendMessage();
                              },
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
            Visibility(
              visible: isAppActivityRunning.value,
              child: loaderWidget(),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> sendMessage() async {
    if (_messageCont.text.trim().isNotEmpty) {
      hideKeyboard();
      setState(() {
        showAppActivity();
      });
      var result = await saveRideHelpMessage(
        HelpCommentRequest(
          userId: userId,
          addedBy: "rider",
          complaintId: widget.helpData.id.toString(),
          comment: _messageCont.text.trim(),
        ),
      );

      if (result == null || result.status == false) {
        toast("Something went wrong");
      } else {
        _messageCont.text = "";
        _messages.insert(0, result.data!);
      }
      setState(() {
        hideAppActivity();
      });
    }
  }
}

class RideHelpChatWidget extends StatefulWidget {
  final RideHelpComment data;

  RideHelpChatWidget({required this.data});

  @override
  _RideHelpChatWidgetState createState() => _RideHelpChatWidgetState();
}

class _RideHelpChatWidgetState extends State<RideHelpChatWidget> {
  String? images;

  void initState() {
    super.initState();
    init();
  }

  init() async {}

  @override
  Widget build(BuildContext context) {
    String time;

    // DateTime date = DateTime.fromMillisecondsSinceEpoch(
    //     widget.data.createdAt.millisecondsSinceEpoch);

    time = DateFormat('dd-MMM-yy, hh:mm a').format(
        DateTime.fromMillisecondsSinceEpoch(
            widget.data.createdAt.millisecondsSinceEpoch));

    Widget chatItem(String? messageTypes) {
      switch (messageTypes) {
        case "text":
          return Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: widget.data.addedBy == UserType.rider
                ? CrossAxisAlignment.end
                : CrossAxisAlignment.start,
            children: [
              Text(widget.data.comment,
                  style: TextStyle(
                    color: Colors.black,
                  ),
                  maxLines: null),
              const SizedBox(height: 8),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    time,
                    style: primaryTextStyle(
                      color: Colors.black,
                      size: 10,
                    ),
                  ),
                ],
              ),
            ],
          );

        default:
          return Container();
      }
    }

    return GestureDetector(
      onLongPress: widget.data.addedBy == UserType.rider ? null : () async {},
      child: Container(
        margin: const EdgeInsets.only(top: 2, bottom: 2),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: widget.data.addedBy == UserType.rider
              ? CrossAxisAlignment.end
              : CrossAxisAlignment.start,
          mainAxisAlignment: widget.data.addedBy == UserType.rider
              ? MainAxisAlignment.end
              : MainAxisAlignment.start,
          children: [
            Container(
              margin: widget.data.addedBy == UserType.rider
                  ? EdgeInsets.only(
                      top: 0.0,
                      bottom: 0.0,
                      left: isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25,
                      right: 8)
                  : EdgeInsets.only(
                      top: 2.0,
                      bottom: 2.0,
                      left: 8,
                      right:
                          isRTL ? 0 : MediaQuery.sizeOf(context).width * 0.25),
              padding: const EdgeInsets.all(10),
              decoration: BoxDecoration(
                boxShadow: Globals.isDarkModeOn ? null : defaultBoxShadow(),
                color: Colors.grey.shade200,

                // widget.data.addedBy == UserType.rider
                //     ? Colors.grey.shade200
                //     : Globals.isDarkModeOn
                //         ? Colors.grey
                //         : Colors.grey[300],
                borderRadius: widget.data.addedBy == UserType.rider
                    ? BorderRadius.only(
                        bottomLeft: radiusCircular(12),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(12),
                        topRight: radiusCircular(12))
                    : BorderRadius.only(
                        bottomLeft: radiusCircular(0),
                        topLeft: radiusCircular(12),
                        bottomRight: radiusCircular(12),
                        topRight: radiusCircular(12)),
              ),
              child: chatItem("text"),
            ),
            const SizedBox(
              height: 10,
            ),
          ],
        ),
      ),
    );
  }

  Color _getTimeColor(String addedBy) {
    return (addedBy == UserType.rider) ? Colors.white : Colors.black;
  }
}

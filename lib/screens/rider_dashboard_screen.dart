import 'dart:ui' as ui;
import 'package:rider/app_exports.dart';
import 'package:rider/features/select_location/screens/select_location_screen.dart';
import 'package:rider/interactive_onboarding.dart';
import 'package:rider/model/NearByDriverListModel.dart';
import 'package:rider/model/TextModel.dart';
import 'package:rider/screens/dashboard_mapbox_utils.dart';
import 'package:rider/screens/SimpleURLWebViewScreen.dart';
import 'package:rider/screens/pickup_screen.dart';
import 'package:rider/services/ads_service.dart';
import 'package:rider/utils/DataProvider.dart';
import 'package:flutter/services.dart';
import 'package:smooth_page_indicator/smooth_page_indicator.dart';
import '../global/models/ride_model.dart';

class RiderDashBoardScreen extends StatefulWidget {
  final int selectedType;
  final bool isRecentSignUp;

  const RiderDashBoardScreen(
      {super.key, this.selectedType = 0, required this.isRecentSignUp});
  @override
  RiderDashBoardScreenState createState() => RiderDashBoardScreenState();
}

class RiderDashBoardScreenState extends State<RiderDashBoardScreen>
    with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  final _controller = PageController();

  List<TexIModel> list = getBookList();

  late PolylinePoints polylinePoints;
  OnRideRequest? servicesListData;

  double cameraZoom = 17;
  double cameraTilt = 0;
  double cameraBearing = 30;
  int onTapIndex = 0;

  int selectIndex = 0;
  String sourceLocationTitle = '';

  List<NearByDriverListModel>? nearDriverModel;

  bool _isLoadingHomePageData = true;
  DateTime? _pickupDate;
  TimeOfDay? _pickupTime;
  HomePageData homePageData = HomePageData(
    blogs: [],
    features: [],
    scheduledRides: [],
    schedule_ride_time: 0,
    advance_booking_limit: 0,
  );

  bool? isScheduledRide;
  DateTime? scheduledDate;

  DashboardMapBoxUtils? _mapBoxUtils;
  bool _locationPermissionEnabled = false;
  bool _checkingForLocationPermission = true;
  bool _mapIsLoading = true;
  bool _isAppRunning = true;

  Future<void> _checkLocationPermission() async {
    PermissionStatus status = await Permission.locationWhenInUse.status;
    if (status == PermissionStatus.granted) {
      await onLocationPermissionGranted();
    }
    setState(() {
      _checkingForLocationPermission = false;
      _locationPermissionEnabled = status == PermissionStatus.granted;
    });
  }

  Future<void> onLocationPermissionGranted() async {
    if (_mapBoxUtils != null) {
      setState(() {
        _locationPermissionEnabled = true;
      });
      if (!_mapBoxUtils!.isLocationTrackingActive) {
        var currentContext = context;
        if (!widget.isRecentSignUp) {
          // showAppActivityDialog(
          //     context: currentContext, title: "Fetching your location...");
        }
        await _mapBoxUtils!.startLocationTracking();
        if (!widget.isRecentSignUp && currentContext.mounted) {
          // Navigator.of(currentContext).pop();
        }

        currentContext = context;
        if (!widget.isRecentSignUp && currentContext.mounted) {
          // showAppActivityDialog(
          //     context: currentContext, title: "Map is loading...");
        }
        _mapBoxUtils!.onLocationChanged = (locationData) {
          if (!widget.isRecentSignUp &&
              currentContext.mounted &&
              _mapIsLoading) {
            _mapIsLoading = false;
            // Navigator.of(currentContext).pop();
          }
          setState(() {
            // Trigger UI update if needed
          });
        };
      }
    }
  }

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);

    super.initState();
    Globals.homePageDataRefresher = () {
      setState(() {
        _isLoadingHomePageData = true;
      });
      getDataForHomePage();
    };
    init();
  }

  @override
  void dispose() {
    _mapBoxUtils?.dispose();

    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    switch (state) {
      case AppLifecycleState.resumed:
        _isAppRunning = true;
        _checkLocationPermission();
        _ensureMapIsCentered();
      default:
        _isAppRunning = false;
    }
  }

  @override
  void didChangePlatformBrightness() {
    if (_isAppRunning && _mapBoxUtils != null) {
      _mapBoxUtils!.changeMapTheme(Theme.of(context));
    }
    super.didChangePlatformBrightness();
  }

  Future<void> appSetting() async {
    await getAppSettingApi().then((value) {
      if (!value.status) {
        toast(Globals.language.errorMsg);
        return;
      }
      Globals.companyEmail = value.data!.settingModel?.contactEmail ?? '';

      // PDF_CONTACT_NUMBER = value.twilio ?? '';
    }).onError((error, stackTrace) {
      log(error.toString());
      handleError(error, stackTrace);
    });
  }

  Future init() async {
    getDataForHomePage();
    _checkLocationPermission();
    appSetting();
    
    // Fallback: Ensure map recenters after 3 seconds if location tracking hasn't started
    Future.delayed(const Duration(seconds: 3), () {
      if (_mapBoxUtils != null && 
          !_mapBoxUtils!.isLocationTrackingActive && 
          Globals.currentLocation != null) {
        _mapBoxUtils!.flyToCurrentLocation();
      }
    });
  }

  Future<Uint8List> getBytesFromAsset(String path) async {
    double pixelRatio = MediaQuery.of(context).devicePixelRatio;
    ByteData data = await rootBundle.load(path);
    ui.Codec codec = await ui.instantiateImageCodec(data.buffer.asUint8List(),
        targetWidth: pixelRatio.round() * 60);
    ui.FrameInfo fi = await codec.getNextFrame();
    return (await fi.image.toByteData(format: ui.ImageByteFormat.png))!
        .buffer
        .asUint8List();
  }

  Future<void> getDataForHomePage() async {
    var result = await getHomePageData();

    if (result != null) {
      _isLoadingHomePageData = false;
      setState(() {
        homePageData = result;
      });

      if (widget.isRecentSignUp) {
        await _closeTopContent();
        InteractiveOnboarding.start(_initialPermisionsNotifier);
      } else {
        _initialPermisionsNotifier();
      }
    } else {
      showErrorToast();
    }
  }

  Future<void> _initialPermisionsNotifier() async {
    BuildContext widgetContext = context;

    await HelperMethods.handleLocationMustBeEnabled(
      widgetContext,
      message:
          "To book a ride, Please allow location permission. ROOO will use your location to find the nearest drivers.",
      isMandatory: true,
    );
    if (widgetContext.mounted) {
      await HelperMethods.handleNotificationMustBeEnabled(
        widgetContext,
        message:
            "To get the best experience, Please allow notification permission. ROOO will send you account related notifications, new offers etc.",
        isMandatory: true,
      );
    }
  }

  Future<void> _closeTopContent() async {
    final NavigatorState navigator = Navigator.of(context);

    bool shouldContinue = true;
    while (mounted && shouldContinue && navigator.canPop()) {
      if (mounted) {
        final isCurrent = ModalRoute.of(context)?.isCurrent == true;
        if (isCurrent) {
          shouldContinue = false;
          continue;
        }
      } else {
        break;
      }

      navigator.pop();
      await Future.delayed(const Duration(milliseconds: 100));
    }

    if (mounted) {
      Globals.homePageRedirect();
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  List<HomePageBlogData> cardsList = [];

  Widget _getNoRideView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Center(child: AppAdWidget(adType: AdType.dashboard)),
        (!_isLoadingHomePageData && homePageData.scheduledRides.isNotEmpty)
            ? Padding(
                padding: const EdgeInsets.only(top: 14.0),
                child: Text(
                  Globals.language.scheduledRides,
                  style: const TextStyle(
                    color: AppColors.lightThemePrimaryColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
              )
            : const SizedBox(),
        ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount:
                _isLoadingHomePageData ? 0 : homePageData.scheduledRides.length,
            padding:
                const EdgeInsets.only(top: 8, bottom: 8, left: 16, right: 16),
            itemBuilder: (_, index) {
              OnRideRequest data = homePageData.scheduledRides[index];
              return IntrinsicHeight(
                child: inkWellWidget(
                  onTap: () {
                    Globals.homePageToMyRidesNavigator();
                  },
                  child: Container(
                    padding: const EdgeInsets.only(top: 8, bottom: 8),
                    margin: const EdgeInsets.only(top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(10),
                      boxShadow: [
                        BoxShadow(
                            color: Colors.grey.withAlpha(102), // 0.4 opacity
                            blurRadius: 10,
                            spreadRadius: 0,
                            offset: const Offset(0.0, 0.0)),
                      ],
                    ),
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Row(
                                children: [
                                  const Icon(
                                    Ionicons.calendar,
                                    size: 16,
                                    color: Colors.black,
                                  ),
                                  const SizedBox(width: 8),
                                  Padding(
                                    padding: const EdgeInsets.only(top: 2),
                                    child: Text(
                                        printDate(data.datetime.validate()),
                                        style: Globals.isDarkModeOn
                                            ? const TextStyle(
                                                color: Colors.black,
                                                fontSize: 14)
                                            : primaryTextStyle(size: 14)),
                                  ),
                                ],
                              ),
                            ],
                          ),
                          const Divider(
                            height: 24,
                            thickness: 0.5,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Column(
                                children: [
                                  Icon(Icons.near_me,
                                      color: Colors.green, size: 18),
                                  SizedBox(
                                    height: 65,
                                    child: DottedLine(
                                      direction: Axis.vertical,
                                      lineLength: double.infinity,
                                      lineThickness: 1,
                                      dashLength: 2,
                                    ),
                                  ),
                                  Icon(Icons.location_on,
                                      color: Colors.red, size: 18),
                                ],
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    SizedBox(
                                      height: 58,
                                      child: Text(
                                        data.startAddress.validate(),
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                    const SizedBox(height: 20),
                                    SizedBox(
                                      height: 58,
                                      child: Text(
                                        data.endAddress.validate(),
                                        style: const TextStyle(
                                          color: Colors.black,
                                          fontSize: 14,
                                        ),
                                        maxLines: 2,
                                        overflow: TextOverflow.ellipsis,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            }),
        const SizedBox(
          height: 20,
        ),
        const Divider(
          thickness: 1,
        ),
        const Text(
          "ROOO Reads",
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        const SizedBox(
          height: 12,
        ),
        SizedBox(
          height: 175,
          child: _isLoadingHomePageData
              ? const Center(
                  child: CircularProgressIndicator(),
                )
              : Stack(
                  children: [
                    PageView(
                      controller: _controller,
                      children: [
                        for (int i = 0; i < homePageData.blogs.length; i++)
                          InkWell(
                            onTap: () {
                              launchScreen(
                                SimpleWebViewURLScreen(
                                  title: homePageData.blogs[i].title,
                                  dataFetcher:
                                      getBlogDetails(homePageData.blogs[i].id),
                                  htmlDataKey: null,
                                ),
                              );
                            },
                            child: Padding(
                              padding: const EdgeInsets.all(2.0),
                              child: Card(
                                margin: EdgeInsets.zero,
                                shape: RoundedRectangleBorder(
                                  side: BorderSide(color: Colors.grey[300]!),
                                  borderRadius: BorderRadius.circular(12.0),
                                ),
                                color: Color(
                                  int.parse(
                                    homePageData.blogs[i].backgroundColor,
                                  ),
                                ),
                                child: Row(
                                  children: [
                                    Expanded(
                                      child: Padding(
                                        padding: const EdgeInsets.all(14),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              child: Padding(
                                                padding: const EdgeInsets.only(
                                                  top: 20,
                                                ),
                                                child: Text(
                                                  homePageData.blogs[i].title,
                                                  maxLines: 3,
                                                  overflow:
                                                      TextOverflow.ellipsis,
                                                  style: TextStyle(
                                                    color: Color(
                                                      int.parse(
                                                        homePageData
                                                            .blogs[i].textColor,
                                                      ),
                                                    ),
                                                    fontSize: 16,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ),
                                            ),
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 28),
                                              child: Row(children: [
                                                Expanded(
                                                  child: Text(
                                                    homePageData
                                                        .blogs[i].subTitle,
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                    style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 12,
                                                      color: Color(
                                                        int.parse(
                                                          homePageData.blogs[i]
                                                              .textColor,
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                ),
                                                const SizedBox(
                                                  width: 2,
                                                ),
                                                Icon(
                                                  Icons.arrow_forward,
                                                  color: Color(
                                                    int.parse(
                                                      homePageData
                                                          .blogs[i].textColor,
                                                    ),
                                                  ),
                                                ),
                                              ]),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    CachedNetworkImage(
                                      imageUrl: homePageData.blogs[i].imageURL,
                                      placeholder: (context, url) =>
                                          const SizedBox(
                                        width: 80,
                                        child: Center(
                                          child: CircularProgressIndicator(),
                                        ),
                                      ),
                                      errorWidget: (context, url, error) =>
                                          const SizedBox(
                                        width: 80,
                                        child: Center(
                                          child: Icon(
                                            Icons.error,
                                          ),
                                        ),
                                      ),
                                      imageBuilder: (context, imageProvider) =>
                                          Container(
                                        width: 80,
                                        decoration: BoxDecoration(
                                          borderRadius: const BorderRadius.only(
                                            topRight: Radius.circular(
                                              12,
                                            ),
                                            bottomRight: Radius.circular(12),
                                          ),
                                          image: DecorationImage(
                                            image: imageProvider,
                                            fit: BoxFit.cover,
                                          ),
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            ),
                          )
                      ],
                    ),
                    Positioned(
                      bottom: 10,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: SmoothPageIndicator(
                          controller: _controller,
                          count: homePageData.blogs.length,
                          effect: JumpingDotEffect(
                            activeDotColor: Colors.black,
                            dotColor: Colors.grey.withAlpha(128), // 0.5 opacity
                            dotHeight: 10,
                            dotWidth: 10,
                            verticalOffset: 8,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
        ),
        const SizedBox(
          height: 20,
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarBrightness: Brightness.light,
      statusBarIconBrightness: Brightness.dark,
    ));

    return Scaffold(
        body: Stack(
      children: [
        Stack(
          children: [
            Stack(
              children: [
                DashboardMapWidget(
                  key: const ValueKey("DashboardMap"),
                  onMapBoxUtilsCreated: (mapBoxUtils) {
                    setState(() {
                      _mapBoxUtils = mapBoxUtils;
                    });
                    _checkLocationPermission();
                  },
                ),
                _locationPermissionEnabled || _checkingForLocationPermission
                    ? const SizedBox()
                    : Center(child: mapOverlayerIfNotGranted(context)),
              ],
            ),
            Positioned(
              right: 16,
              bottom: 106,
              child: FloatingActionButton(
                onPressed: () async {
                  if (_mapBoxUtils?.isLocationTrackingActive == true) {
                    await _mapBoxUtils!.flyToCurrentLocation();
                  } else {
                    bool isLocationEnabled =
                        await HelperMethods.handleLocationMustBeEnabled(
                            context);
                    if (isLocationEnabled) {
                      await onLocationPermissionGranted();
                    }
                  }
                },
                child: const Icon(Icons.my_location),
              ),
            ),
          ],
        ),
        _getTopWidget(),
        SlidingUpPanel(
            color: checkIfDarkModeIsOn(context) ? Colors.black : Colors.white,
            padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
            borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(30), topRight: Radius.circular(30)),
            backdropTapClosesPanel: true,
            minHeight: 70,
            panelBuilder: (sc) {
              return SingleChildScrollView(
                controller: sc,
                child: Padding(
                  padding: const EdgeInsets.only(top: 18.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Center(
                        child: Container(
                          alignment: Alignment.center,
                          margin: const EdgeInsets.only(bottom: 16),
                          height: 3,
                          width: 90,
                          decoration: BoxDecoration(
                            color: Colors.grey,
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      _getNoRideView(),
                    ],
                  ),
                ),
              );
            }),
      ],
    ));
    // );
  }

  @override
  bool get wantKeepAlive => true;

  Widget _getTopWidget() {
    return Padding(
      padding: EdgeInsets.only(
        top: Platform.isIOS ? 50 : 40,
        left: 10,
        right: 10,
      ),
      child: Card(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(30),
        ),
        child: Container(
          height: 50,
          padding: const EdgeInsets.all(4),
          decoration: BoxDecoration(
              color: AppColors.primaryBlackColor,
              borderRadius: BorderRadius.circular(30)),
          child: InkWell(
            onTap: () async {
              if (!Globals.user.isProfileComplete) {
                _handleIncompleteProfile();
                return;
              }

              bool isLocationEnabled =
                  await HelperMethods.handleLocationMustBeEnabled(context);
              if (!isLocationEnabled) {
                return;
              }
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const SelectLocationScreen(
                    scheduledTime: null,
                  ),
                ),
              );
            },
            child: Row(
              children: [
                const SizedBox(width: 12),
                Icon(
                  Icons.search,
                  color: AppColors.primaryWhiteColor,
                ),
                const SizedBox(width: 12),
                Text(
                  key: Globals.instantRideWidgetKey,
                  Globals.language.whereToGoText,
                  style: TextStyle(color: AppColors.primaryWhiteColor),
                ),
                const Spacer(),
                InkWell(
                  onTap: () async {
                    if (!Globals.user.isProfileComplete) {
                      _handleIncompleteProfile();
                      return;
                    }
                    bool isLocationEnabled =
                        await HelperMethods.handleLocationMustBeEnabled(
                            context);
                    if (!isLocationEnabled) {
                      return;
                    }

                    await Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => PickupScreen(
                          maxBookingDay: homePageData.advance_booking_limit,
                          minScheduleBookingMinutes:
                              homePageData.schedule_ride_time,
                        ),
                      ),
                    );
                  },
                  child: Padding(
                    key: Globals.scheduledRideWidgetKey,
                    padding: const EdgeInsets.all(8.0),
                    child: Container(
                      height: 50,
                      decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(30)),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const SizedBox(width: 8),
                          const Icon(
                            Icons.timer_rounded,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            _pickupDate == null
                                ? Globals.language.now
                                : '${dateToString(_pickupDate!)} ${timeToString(_pickupTime!)}',
                            style: const TextStyle(color: Colors.white),
                          ),
                          const SizedBox(width: 4),
                          const Icon(
                            Icons.keyboard_arrow_down,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 8),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _handleIncompleteProfile() {
    showAppDialog(
      dialogType: AppDialogType.error,
      title: "Your profile is incomplete. Please complete your profile",
      onAccept: () {
        Navigator.of(context).pop();
        launchScreen(
          RegisterScreen(
            countryDialCode: "+61",
            countryISOCode: "AU",
            phone: "",
            isSocialLogin: true,
            oldUser: Globals.user,
          ),
        );
      },
    );
  }

  Future<void> _ensureMapIsCentered() async {
    if (_mapBoxUtils != null && 
        _mapBoxUtils!.isLocationTrackingActive && 
        Globals.currentLocation != null) {
      await Future.delayed(const Duration(milliseconds: 300));
      await _mapBoxUtils!.flyToCurrentLocation();
    }
  }
}

Widget mapOverlayerIfNotGranted(BuildContext context) {
  return Container(
    margin: const EdgeInsets.all(16),
    padding: const EdgeInsets.all(20),
    decoration: BoxDecoration(
      color: Colors.white.withOpacity(0.15),
      borderRadius: BorderRadius.circular(20),
      boxShadow: const [
        BoxShadow(
          // color: Colors.white.withOpacity(0.1),
          blurRadius: 100,
          spreadRadius: 3,
        ),
      ],
    ),
    child: Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        const Text(
          "Location Permission Required",
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          "To view your current location on the map, please grant the location permission.",
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 14,
            color: Colors.white.withOpacity(0.8),
          ),
        ),
        const SizedBox(height: 24),
        AppButton(
          text: "Grant Permission",
          onPressed: () async {
            HelperMethods.handleLocationMustBeEnabled(context);
          },
        ),
      ],
    ),
  );
}

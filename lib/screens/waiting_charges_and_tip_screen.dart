import 'package:rider/app_exports.dart';
import 'package:rider/features/payment/screens/select_payment_for_additional_charges_screen.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:rider/services/ads_service.dart';

import '../global/models/ride_model.dart';

class WaitingChargesAndTipScreen extends StatefulWidget {
  final RideModel request;

  const WaitingChargesAndTipScreen({
    super.key,
    required this.request,
  });

  @override
  WaitingChargesAndTipScreenState createState() =>
      WaitingChargesAndTipScreenState();
}

class WaitingChargesAndTipScreenState
    extends State<WaitingChargesAndTipScreen> {
  GlobalKey<FormState> formKey = GlobalKey<FormState>();

  TextEditingController tipController = TextEditingController();
  TextEditingController reviewController = TextEditingController();

  num rattingData = 5;
  int currentIndex = -1;
  bool isMoreTip = false;
  List<PaymentModel> paymentList = [];
  String stripPaymentKey = '';
  String stripPaymentPublishKey = '';

  OnRideRequest? servicesListData;

  num? amount;
  String? holdPaymentId;
  String? paymentCardId;
  bool isTestType = true;

  // Review related variables
  List<dynamic> allMsgs = [];
  dynamic filteredMsgs;
  bool isLoadingMsgs = true;

  @override
  void initState() {
    hideAppActivity();
    Globals.isWaitingAndTipChargesScreenOpened = true;
    super.initState();
    init();
  }

  void init() async {
    // getSuggestedMsgs();
  }

  void getFilteredMsgs() {
    filteredMsgs = allMsgs
        .where(
          (element) => element['rating'] == rattingData,
        )
        .first;
    setState(() {
      hideAppActivity();
    });
  }

  Future<void> getSuggestedMsgs() async {
    showAppActivity();
    getSuggestedReviewMsgs().then((value) {
      if (value == null) {
        toast(Globals.language.errorMsg);
      } else {
        if (value['status'] == true) {
          allMsgs = value['data'];
          getFilteredMsgs();
          setState(() {
            hideAppActivity();
          });
        } else {
          toast(value['message'] ?? Globals.language.errorMsg);
        }
      }
    }).onError((error, stackTrace) {
      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });
  }

  @override
  dispose() {
    hideAppActivity();
    Globals.isWaitingAndTipChargesScreenOpened = false;
    super.dispose();
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  Future<void> saveData() async {
    showAppActivity();
    num refund = 0;
    if (holdPaymentId == null) {
      refund = (widget.request.charges?.refundableAmount ?? 0) -
          (num.tryParse(tipController.text.trim()) ?? 0);
      refund = num.parse(refund.toStringAsFixed(2));
      amount = 0;
    }
    await saveWaitingTimeAndTipPreAuth(
      walletAmount: null,
      holdPaymentId: holdPaymentId,
      paymentCardId: paymentCardId,
      rideRequestId: widget.request.onRideRequest!.id!,
      advanced_paid: widget.request.charges?.advancedPaid,
      due_amount: widget.request.charges?.dueAmount,
      refundable_amount: refund,
      tips: tipController.text.trim().isEmpty
          ? null
          : num.tryParse(tipController.text.trim()),
      waiting_charges: widget.request.charges?.waitingCharges,
      service_id: widget.request.onRideRequest!.serviceId!,
      pre_auth_amount: amount,
      rating: rattingData,
      comment: reviewController.text.trim(),
    ).then((value) async {
      if (value.status) {
        launchScreen(const DashboardWrapperScreen(), isNewTask: true);
      } else {
        setState(() {});
        toast(value.message);
      }
    }).onError((error, stackTrace) {
      setState(() {});
      toast(Globals.language.errorMsg);
      handleError(error, stackTrace);
    });
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        hideKeyboard();
      },
      child: PopScope(
        canPop: false,
        child: Scaffold(
          appBar: const RoooAppbar(
            title: "Complete Your Ride",
            hideBackButton: true,
            isDarkOverlay: false,
          ),
          bottomNavigationBar: Padding(
            padding: EdgeInsets.fromLTRB(
              Layout.scaffoldBodyPadding,
              Layout.scaffoldBodyPadding,
              Layout.scaffoldBodyPadding,
              Platform.isIOS ? 15 * 2 : 15,
            ),
            child: _buildContinueButton(),
          ),
          body: Stack(
            children: [
              SingleChildScrollView(
                child: Column(
                  children: [
                    if ((widget.request.charges?.items ?? []).isNotEmpty ||
                        widget.request.charges != null)
                      _buildChargesSection(),
                    // _DriverIllustrationWidget(),
                    _buildReviewSection(),
                    _buildTipSection(),
                  ],
                ),
              ),
              const ActivityIndicator(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildChargesSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          // Charges items
          if ((widget.request.charges?.items ?? []).isNotEmpty)
            ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                var data = widget.request.charges!.items![index];
                return ListTile(
                  contentPadding: EdgeInsets.zero,
                  title: Text(
                    data.title!,
                    style: const TextStyle(color: Colors.white),
                  ),
                  trailing: Text(
                    Constants.currencySymbol + data.amount.toString(),
                    style: const TextStyle(color: Colors.white),
                  ),
                );
              },
              separatorBuilder: (context, index) =>
                  const Divider(color: Colors.grey),
              itemCount: (widget.request.charges?.items ?? []).length,
            ),

          // Due amount
          if (widget.request.charges != null)
            ListTile(
              contentPadding: EdgeInsets.zero,
              title: const Text(
                "Due Amount",
                style:
                    TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
              ),
              trailing: Text(
                '${Constants.currencySymbol} ${widget.request.charges!.dueAmount}',
                style: const TextStyle(
                    color: Colors.white, fontWeight: FontWeight.bold),
              ),
            ),

          // Info message
          if (widget.request.charges != null)
            Container(
              margin: const EdgeInsets.only(top: 16),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: const Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: Colors.orange,
                    size: 20,
                  ),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      "You are required to pay these extra charges.",
                      style: TextStyle(
                        fontStyle: FontStyle.italic,
                        color: Colors.orange,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildTipSection() {
    return Container(
      margin: const EdgeInsets.only(
        left: 16,
        right: 16,
        bottom: 16,
      ),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _TipHeaderWidget(),
          const SizedBox(height: 16),
          _buildTipAmountButtons(),
          const SizedBox(height: 24),
          _CustomAmountWidget(
            controller: tipController,
            onChanged: (value) {
              setState(() {
                currentIndex = -1;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildTipAmountButtons() {
    final tipAmounts = ["10", "20", "30"];
    final tipLabels = ["Thanks", "Great", "Super"];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: tipAmounts.asMap().entries.map((entry) {
        int index = entry.key;
        String amount = entry.value;
        String label = tipLabels[index];
        bool isSelected = currentIndex == index;

        return _TipAmountButton(
          amount: amount,
          label: label,
          isSelected: isSelected,
          onTap: () {
            currentIndex = index;
            tipController.text = amount;
            tipController.selection = TextSelection.fromPosition(
              TextPosition(offset: amount.length),
            );
            setState(() {});
          },
        );
      }).toList(),
    );
  }

  Widget _buildContinueButton() {
    return SizedBox(
      width: double.infinity,
      child: AppButtonWidget(
        text: Globals.language.continueD,
        textStyle: boldTextStyle(color: Colors.white),
        color: Theme.of(context).primaryColor,
        onTap: () {
          hideKeyboard();
          int tipAmount = (int.tryParse(tipController.text.trim()) ?? 0);
          if (tipAmount > 0 && tipAmount < 10) {
            toast("Minimum tip is AUD 10");
            return;
          }
          else {
            num payableAmount =
                (tipAmount + (widget.request.charges?.dueAmount ?? 0)) -
                    (widget.request.charges?.refundableAmount ?? 0);
            if (payableAmount <= 0) {
              saveData();
              return;
            }

            num refund =
                (widget.request.charges?.refundableAmount ?? 0) - payableAmount;
            refund = refund < 0 ? 0 : refund;

            launchScreen(
              SelectPaymentForAdditionalCharges(
                payableAmount: payableAmount,
                rideId: widget.request.onRideRequest!.id!,
                chargesType: AdditionalChargesType.tip,
                tipAndWaitingData: TipAndWaitingData(
                  advancedPaid: widget.request.charges?.advancedPaid,
                  dueAmount: widget.request.charges?.dueAmount,
                  refundableAmount: refund,
                  waitingCharges: widget.request.charges?.waitingCharges,
                  serviceId: widget.request.onRideRequest!.serviceId!,
                  tips: tipAmount > 0 ? tipAmount : null,
                  rating: rattingData,
                  comment: reviewController.text.trim(),
                ),
              ),
            );
          }
        },
      ),
    );
  }

  Widget _reviewDriverInfo() {
    return Row(
      children: [
        ClipRRect(
          borderRadius: BorderRadius.circular(35),
          child: commonCachedNetworkImage(
            (widget.request.driver?.profileImage ?? '').validate(),
            height: 80,
            width: 80,
            fit: BoxFit.cover,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '${widget.request.driver?.firstName ?? ''.validate()} ${widget.request.driver?.lastName ?? ''.validate()}',
                style: boldTextStyle(
                  size: 17,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Your Driver',
                style: primaryTextStyle(
                  color: Colors.grey[400],
                  size: 14,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildReviewSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.grey[900],
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Driver info section
          // _reviewDriverInfo(),
          // const SizedBox(height: 24),
          // Rating section
          Text(
            "How was your ride?",
            style: boldTextStyle(size: 18, color: Colors.white),
          ),
          const SizedBox(height: 10),
          Center(
            child: RatingBar.builder(
              initialRating: rattingData * 1,
              direction: Axis.horizontal,
              glow: false,
              allowHalfRating: false,
              itemCount: 5,
              itemPadding: const EdgeInsets.symmetric(horizontal: 4),
              itemBuilder: (context, _) =>
                  const Icon(Icons.star, color: Colors.amber),
              onRatingUpdate: (rating) {
                // formKey.currentState!.reset();
                rattingData = rating;
                setState(() {});
                // reviewController.text = '';
                // getFilteredMsgs();
              },
            ),
          ),
          const SizedBox(height: 14),
          // Review text field
          Text("Add Review", style: boldTextStyle(color: Colors.white)),
          const SizedBox(height: 12),
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[800],
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey[700]!, width: 1),
            ),
            child: AppTextField(
              isValidationRequired: rattingData < 2,
              controller: reviewController,
              decoration:
                  inputDecoration(context, label: "Your review...").copyWith(
                fillColor: Colors.transparent,
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
              ),
              textFieldType: TextFieldType.OTHER,
              minLines: 3,
              maxLines: 5,
              errorThisFieldRequired: Globals.language.thisFieldRequired,
            ),
          ),
          // const SizedBox(height: 16),

          // Suggested reviews
          // if ((filteredMsgs?['messages'] ?? []).isNotEmpty) ...[
          //   const SizedBox(height: 16),
          //   Text(
          //     Globals.language.suggestedReview,
          //     style: boldTextStyle(color: Colors.white),
          //   ),
          //   const SizedBox(height: 12),
          //   Wrap(
          //     spacing: 8,
          //     runSpacing: 8,
          //     children: (filteredMsgs?['messages'] ?? [])
          //         .map<Widget>(
          //           (e) => InkWell(
          //             onTap: () {
          //               reviewController.text = e;
          //             },
          //             child: Container(
          //               padding: const EdgeInsets.symmetric(
          //                 horizontal: 12,
          //                 vertical: 8,
          //               ),
          //               decoration: BoxDecoration(
          //                 color: Colors.grey[700],
          //                 borderRadius: BorderRadius.circular(20),
          //                 border: Border.all(
          //                   color: Colors.grey[600]!,
          //                   width: 1,
          //                 ),
          //               ),
          //               child: Text(
          //                 e,
          //                 style: primaryTextStyle(
          //                   color: Colors.white,
          //                   size: 12,
          //                 ),
          //               ),
          //             ),
          //           ),
          //         )
          //         .toList(),
          //   ),
          // ],
          // const SizedBox(height: 16),
          // const Center(
          //   child: AppAdWidget(adType: AdType.rideReview),
          // ),
        ],
      ),
    );
  }
}

// Driver Illustration Widget
class _DriverIllustrationWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: 350,
      width: 350,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(60),
        // color: Colors.grey[800],
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          Image.asset('assets/images/driver_illustration.png'),
          // Container(
          //   height: 80,
          //   width: 80,
          //   decoration: BoxDecoration(
          //     borderRadius: BorderRadius.circular(40),
          //     color: const Color(0xFFD4A574), // Skin tone color
          //   ),
          //   child: const Icon(
          //     Icons.person,
          //     size: 50,
          //     // color: Colors.black54,
          //   ),
          // ),
          // Sparkle effects
          const Positioned(
            top: 10,
            left: 20,
            child: Icon(
              Icons.star,
              color: Colors.yellow,
              size: 16,
            ),
          ),
          const Positioned(
            top: 20,
            right: 15,
            child: Icon(
              Icons.star,
              color: Colors.yellow,
              size: 12,
            ),
          ),
        ],
      ),
    );
  }
}

// Tip Header Widget
class _TipHeaderWidget extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return const Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Want to thank your driver?",
          style: TextStyle(
            color: Colors.white,
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 8),
        Text(
          "Your tip makes their day better!",
          textAlign: TextAlign.center,
          style: TextStyle(
            color: Colors.grey,
            fontSize: 14,
          ),
        ),
      ],
    );
  }
}

// Tip Amount Button Widget
class _TipAmountButton extends StatelessWidget {
  final String amount;
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const _TipAmountButton({
    required this.amount,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return inkWellWidget(
      onTap: onTap,
      child: Container(
        height: 85,
        width: 85,
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).primaryColor.withValues(alpha: 0.9)
              : Colors.grey[700],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color:
                isSelected ? Theme.of(context).primaryColor : Colors.grey[700]!,
            width: 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              '${Constants.currencySymbol} $amount',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            label.toLowerCase() == "thanks"
                ? const Text(
                    "😀",
                    style: TextStyle(fontSize: 16),
                  )
                : label.toLowerCase() == "great"
                    ? const Text(
                        "🤩",
                        style: TextStyle(fontSize: 16, color: Colors.red),
                      )
                    : const Text(
                        "🥳",
                        style: TextStyle(fontSize: 16),
                      ),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Custom Amount Widget
class _CustomAmountWidget extends StatelessWidget {
  final TextEditingController controller;
  final Function(String) onChanged;

  const _CustomAmountWidget({
    required this.controller,
    required this.onChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
      decoration: BoxDecoration(
        color: Colors.grey[800],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey[700]!, width: 1),
      ),
      child: Row(
        children: [
          const Icon(
            Icons.edit,
            color: Colors.grey,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: TextFormField(
              controller: controller,
              style: const TextStyle(color: Colors.white),
              decoration: const InputDecoration(
                hintText: "Enter your own amount",
                hintStyle: TextStyle(color: Colors.grey),
                border: InputBorder.none,
                contentPadding: EdgeInsets.zero,
              ),
              keyboardType: TextInputType.number,
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }
}

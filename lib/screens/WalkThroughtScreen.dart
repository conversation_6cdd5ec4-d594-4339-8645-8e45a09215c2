import 'package:rider/app_exports.dart';

import '../model/WalkThroughModel.dart';

class WalkThroughScreen extends StatefulWidget {
  const WalkThroughScreen({super.key});

  @override
  WalkThroughScreenState createState() => WalkThroughScreenState();
}

class WalkThroughScreenState extends State<WalkThroughScreen> {
  PageController pageController = PageController();
  int currentPage = 0;

  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  List<WalkThroughModel> walkThroughClass = [
    WalkThroughModel(
      name: 'Select your Ride',
      text: "Request a ride get picked up by a nearby community driver",
      img: icWalk1,
    ),
    WalkThroughModel(
      name: 'Navigating your Ride',
      text: "Seamless Travel, Smart Choices\nStress-Free Journeys",
      img: icWalk2,
    ),
    WalkThroughModel(
      name: 'Navigating your Ride',
      text: "Seamless Travel, Smart Choices\nStress-Free Journeys",
      img: icWalk3,
    ),
  ];

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          PageView.builder(
            itemCount: walkThroughClass.length,
            controller: pageController,
            itemBuilder: (context, i) {
              return Stack(
                alignment: Alignment.center,
                children: [
                  Image.asset(
                    walkThroughClass[i].img.toString(),
                    fit: BoxFit.cover,
                    width: MediaQuery.sizeOf(context).width,
                    height: MediaQuery.sizeOf(context).height,
                  ),
                  // Positioned(
                  //   bottom: Platform.isIOS ? 150 : 120,
                  //   left: 10,
                  //   right: 10,
                  //   child: Column(
                  //     mainAxisAlignment: MainAxisAlignment.end,
                  //     crossAxisAlignment: CrossAxisAlignment.center,
                  //     children: [
                  //       Text(
                  //         walkThroughClass[i].name!,
                  //         style: boldTextStyle(size: 30, color: Colors.white),
                  //         textAlign: TextAlign.center,
                  //       ),
                  //       const SizedBox(height: 16),
                  //       Text(
                  //         walkThroughClass[i].text.toString(),
                  //         style: const TextStyle(
                  //           fontSize: 16,
                  //           color: Colors.white,
                  //         ),
                  //         textAlign: TextAlign.center,
                  //       ),
                  //     ],
                  //   ),
                  // ),
                ],
              );
            },
            onPageChanged: (int i) {
              currentPage = i;
              setState(() {});
            },
          ),
          Positioned(
            bottom: Platform.isIOS ? 30 : 10,
            right: 0,
            left: 0,
            child: Column(
              children: [
                dotIndicator(walkThroughClass, currentPage),
                GestureDetector(
                  onTap: () {
                    launchScreen(const LoginScreen(), isNewTask: true);
                    // if (currentPage.toInt() >= 2) {
                    //   launchScreen( LoginScreen(), isNewTask: true);
                    //   Globals.sharedPrefs.setBool(IS_FIRST_TIME, false);
                    // } else {
                    //   pageController.nextPage(
                    //       duration: Duration(seconds: 1),
                    //       curve: Curves.linearToEaseOut);
                    // }
                  },
                  child: Container(
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    padding: const EdgeInsets.all(12),
                    child: const Icon(Icons.arrow_forward, color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          // Positioned(
          //   top: 30,
          //   right: 0,
          //   child: TextButton(
          //     onPressed: () {
          //       launchScreen( LoginScreen(), isNewTask: true);
          //       Globals.sharedPrefs.setBool(IS_FIRST_TIME, false);
          //     },
          //     child: Text('Skip', style: boldTextStyle(color: Colors.white)),
          //   ),
          // ),
        ],
      ),
    );
  }
}

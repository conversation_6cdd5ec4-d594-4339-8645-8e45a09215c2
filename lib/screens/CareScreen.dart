import 'package:flutter/services.dart';
import 'package:rider/app_exports.dart';

import 'package:rider/screens/AddCareRequestScreen.dart';
import 'package:rider/screens/all_rides_screen.dart';
import '../components/create_care_tabs_screen.dart';

class CareScreen extends StatefulWidget {
  const CareScreen({super.key});

  @override
  CareScreenState createState() => CareScreenState();
}

class CareScreenState extends State<CareScreen> {
  int currentPage = 1;
  int totalPage = 1;
  List<String> careStatus = [
    Status.pending,
    Status.closed,
  ];

  @override
  void initState() {
    super.initState();
    // init();
  }

  void init() async {
    //
  }

  @override
  void dispose() {
    HelperMethods.getAndApplyCounters();
    hideAppActivity();
    super.dispose();
    if (Platform.isIOS) {
      SystemChrome.setSystemUIOverlayStyle(SystemUiOverlayStyle.dark);
    }
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: careStatus.length,
      child: Scaffold(
        appBar: RoooAppbar(
          title: Globals.language.roooCare,
          isDarkOverlay: false,
        ),
        floatingActionButton: FloatingActionButton(
          backgroundColor: Theme.of(context).primaryColor,
          onPressed: () async {
            bool? result = await showAppDialog(
                upDownButtons: true,
                onAccept: () {
                  Navigator.of(context).pop(false);
                },
                onCancel: () {
                  Navigator.of(context).pop(true);
                },
                positiveButtonText: "General care request",
                negativeButtonText: "Ride issue",
                dialogType: AppDialogType.confirmation,
                title:
                    "Please specify whether you want to create a general care request or report a ride-related issue");
            if (result == true) {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AllRidesScreen(
                    isFromCare: true,
                  ),
                ),
              );
            } else if (result == false) {
              launchScreen(
                const AddCareRequestScreen(),
              );
            }
          },
          child: const Center(
            child: Icon(
              Icons.add,
              color: Colors.white,

              // color: Globals.isDarkModeOn ? Colors.black : Colors.white,
            ),
          ),
        ),
        body: Column(children: [
          tabContainer(tabs: careStatus),
          const SizedBox(
            height: 8,
          ),

          // Container(
          //   height: 40,
          //   margin: EdgeInsets.only(right: 16, left: 16, top: 16),
          //   decoration: BoxDecoration(
          //
          //     borderRadius: appRadius,
          //   ),
          //   child: TabBar(
          //       indicator: BoxDecoration(
          //         borderRadius: appRadius,
          //         color:
          //             Globals.isDarkModeOn ? Color(0xffc2c2c2) :,
          //       ),
          //       labelColor: Colors.white,
          //       unselectedLabel,
          //       labelStyle: boldTextStyle(color: Colors.white, size: 14),
          //       tabs: careStatus.map((e) {
          //         return Tab(
          //           child: Text(changeStatusText(e)),
          //         );
          //       }).toList()),
          // ),
          Expanded(
            child: TabBarView(
              children: careStatus.map((e) {
                return CreateCareTabScreen(
                  status: e,
                );
              }).toList(),
            ),
          ),
        ]),
      ),
    );
  }
}

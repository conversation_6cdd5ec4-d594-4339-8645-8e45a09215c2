import 'package:rider/app_exports.dart';

import 'package:rider/model/app_setting_model.dart';
import 'package:url_launcher/url_launcher.dart';

class AboutScreen extends StatefulWidget {
  final AppSettingModel settingModel;

  const AboutScreen({super.key, required this.settingModel});

  @override
  AboutScreenState createState() => AboutScreenState();
}

class AboutScreenState extends State<AboutScreen> {
  @override
  void initState() {
    super.initState();
    init();
  }

  void init() async {
    //
  }

  @override
  void setState(fn) {
    if (mounted) super.setState(fn);
  }

  @override
  void dispose() {
    hideAppActivity();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: RoooAppbar(
        title: Globals.language.about,
        isDarkOverlay: false,
      ),
      body: Container(
        alignment: Alignment.center,
        padding: const EdgeInsets.all(16),
        child: Column(
          // mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Image.asset(
            //     // 'images/icons/app_icon.png',
            //     Globals.isDarkModeOn
            //         ? 'images/ic_driver_white.png'
            //         : 'images/ic_driver_dark.png',
            //     // height: 100,
            //     width: 240,
            //     fit: BoxFit.contain),
            // SizedBox(height: 26),
            Text(Constants.appName, style: primaryTextStyle(size: 20)),
            const SizedBox(height: 8),
            Text('v1.0', style: secondaryTextStyle()),
            const SizedBox(height: 16),
            Text(
              Globals.aboutUsText,
              style: secondaryTextStyle(),
              maxLines: 8,
              textAlign: TextAlign.justify,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(16),
        child: SizedBox(
          height: 120,
          child: Column(
            children: [
              Text(Globals.language.lblFollowUs, style: boldTextStyle()),
              const SizedBox(height: 8),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: <Widget>[
                  inkWellWidget(
                    onTap: () {
                      if (widget.settingModel.settingModel!.instagramUrl !=
                              null &&
                          widget.settingModel.settingModel!.instagramUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.settingModel!.instagramUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                        toast(Globals.language.txtURLEmpty);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Image.asset(icInsta, height: 35, width: 35),
                    ),
                  ),
                  // inkWellWidget(
                  //   onTap: () {
                  //     if (widget.settingModel.settingModel!.twitterUrl !=
                  //             null &&
                  //         widget.settingModel.settingModel!.twitterUrl!
                  //             .isNotEmpty) {
                  //       launchUrl(
                  //           Uri.parse(widget
                  //               .settingModel.settingModel!.twitterUrl
                  //               .validate()),
                  //           mode: LaunchMode.externalApplication);
                  //     } else {
                  //       toast(Globals.language.txtURLEmpty);
                  //     }
                  //   },
                  //   child: Padding(
                  //     padding: const EdgeInsets.all(10),
                  //     child: Image.asset(
                  //       icTwitterWhite,
                  //       height: 35,
                  //       width: 35,
                  //       color: Theme.of(context).brightness == Brightness.dark
                  //           ? Colors.white
                  //           : Colors.black,
                  //     ),
                  //   ),
                  // ),
                  inkWellWidget(
                    onTap: () {
                      if (widget.settingModel.settingModel!.linkedinUrl !=
                              null &&
                          widget.settingModel.settingModel!.linkedinUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.settingModel!.linkedinUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                        toast(Globals.language.txtURLEmpty);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Image.asset(icLinked, height: 35, width: 35),
                    ),
                  ),
                  inkWellWidget(
                    onTap: () {
                      if (widget.settingModel.settingModel!.facebookUrl !=
                              null &&
                          widget.settingModel.settingModel!.facebookUrl!
                              .isNotEmpty) {
                        launchUrl(
                            Uri.parse(widget
                                .settingModel.settingModel!.facebookUrl
                                .validate()),
                            mode: LaunchMode.externalApplication);
                      } else {
                        toast(Globals.language.txtURLEmpty);
                      }
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(10),
                      child: Image.asset(icFacebook, height: 35, width: 35),
                    ),
                  ),
                  // inkWellWidget(
                  //   onTap: () {
                  //     if (widget.settingModel.contactNumber != null &&
                  //         widget.settingModel.contactNumber!.isNotEmpty) {
                  //       launchUrl(
                  //           Uri.parse(
                  //               'tel:${widget.settingModel.contactNumber.validate()}'),
                  //           mode: LaunchMode.externalApplication);
                  //     } else {
                  //       toast(Globals.language.txtURLEmpty);
                  //     }
                  //   },
                  //   child: Container(
                  //     margin: EdgeInsets.only(right: 16),
                  //     padding: EdgeInsets.all(10),
                  //     child: Icon(
                  //       Icons.call,
                  //       color: Globals.appStore.isDarkMode
                  //           ? Colors.white
                  //           :,
                  //       size: 36,
                  //     ),
                  //   ),
                  // )
                ],
              ),
              const SizedBox(height: 8),
              widget.settingModel.settingModel?.siteCopyright != null &&
                      widget
                          .settingModel.settingModel!.siteCopyright!.isNotEmpty
                  ? Text(
                      widget.settingModel.settingModel!.siteCopyright
                          .validate(),
                      style: secondaryTextStyle(),
                      maxLines: 1)
                  : Text('Copyright' " @${DateTime.now().year} Rooo",
                      style: secondaryTextStyle(size: 12)),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:rider/app_exports.dart';

class CurrentLocationButton extends StatelessWidget {
  final void Function()? animate_map;
  const CurrentLocationButton({super.key, required this.animate_map});

  @override
  Widget build(BuildContext context) {
    return ElevatedButton(
      onPressed: animate_map,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.white,
        shape: const CircleBorder(),
        elevation: 4,
        padding: EdgeInsets.zero,
      ),
      child: const Icon(
        Icons.gps_fixed,
        color: Colors.black,
      ),
    );
  }
}
